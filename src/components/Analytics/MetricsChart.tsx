/**
 * Metrics Chart Component
 * 
 * Interactive charts for workflow metrics visualization
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { 
  Bar<PERSON>hart3, 
  <PERSON><PERSON>hart, 
  PieChart, 
  TrendingUp,
  Calendar,
  DollarSign
} from 'lucide-react';

interface MetricsChartProps {
  data: {
    metrics: {
      totalExecutions: number;
      successRate: number;
      averageExecutionTime: number;
      aiCostMetrics: {
        totalCost: number;
        costByAgent: Record<string, number>;
        totalTokensUsed: number;
      };
      trends: Array<{
        timestamp: string;
        executionCount: number;
        successRate: number;
        averageTime: number;
        totalCost: number;
      }>;
    };
  };
}

type ChartType = 'execution-trends' | 'cost-breakdown' | 'performance-overview' | 'agent-comparison';

export default function MetricsChart({ data }: MetricsChartProps) {
  const [activeChart, setActiveChart] = useState<ChartType>('execution-trends');

  // Generate sample trend data if not available
  const generateSampleTrends = () => {
    const trends = [];
    const now = new Date();
    for (let i = 6; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      trends.push({
        timestamp: date.toISOString().split('T')[0],
        executionCount: Math.floor(Math.random() * 20) + 10,
        successRate: 85 + Math.random() * 15,
        averageTime: 120000 + Math.random() * 60000,
        totalCost: Math.random() * 5 + 2
      });
    }
    return trends;
  };

  const trends = data.metrics.trends?.length > 0 ? data.metrics.trends : generateSampleTrends();

  const chartConfigs = {
    'execution-trends': {
      title: 'Execution Trends',
      description: 'Daily workflow execution patterns',
      icon: <TrendingUp className="h-4 w-4" />
    },
    'cost-breakdown': {
      title: 'Cost Breakdown',
      description: 'AI costs by agent type',
      icon: <DollarSign className="h-4 w-4" />
    },
    'performance-overview': {
      title: 'Performance Overview',
      description: 'Success rate and execution time trends',
      icon: <BarChart3 className="h-4 w-4" />
    },
    'agent-comparison': {
      title: 'Agent Comparison',
      description: 'Performance comparison across agents',
      icon: <PieChart className="h-4 w-4" />
    }
  };

  const renderExecutionTrends = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-7 gap-2">
        {trends.map((trend, index) => (
          <div key={index} className="text-center">
            <div className="text-xs text-gray-600 mb-1">
              {new Date(trend.timestamp).toLocaleDateString('en-US', { weekday: 'short' })}
            </div>
            <div 
              className="bg-blue-500 rounded-t mx-auto"
              style={{ 
                height: `${(trend.executionCount / 30) * 100}px`,
                width: '20px'
              }}
            />
            <div className="text-xs font-medium mt-1">{trend.executionCount}</div>
          </div>
        ))}
      </div>
      <div className="flex justify-between text-xs text-gray-600">
        <span>Executions per day</span>
        <span>Max: {Math.max(...trends.map(t => t.executionCount))}</span>
      </div>
    </div>
  );

  const renderCostBreakdown = () => {
    const totalCost = Object.values(data.metrics.aiCostMetrics.costByAgent).reduce((sum, cost) => sum + cost, 0);
    const colors = ['bg-blue-500', 'bg-green-500', 'bg-purple-500', 'bg-orange-500', 'bg-pink-500'];
    
    return (
      <div className="space-y-4">
        <div className="flex justify-center">
          <div className="relative w-48 h-48">
            {/* Simple pie chart representation */}
            <div className="w-full h-full rounded-full bg-gray-200 relative overflow-hidden">
              {Object.entries(data.metrics.aiCostMetrics.costByAgent).map(([agent, cost], index) => {
                const percentage = (cost / totalCost) * 100;
                return (
                  <div
                    key={agent}
                    className={`absolute inset-0 ${colors[index % colors.length]} opacity-80`}
                    style={{
                      clipPath: `polygon(50% 50%, 50% 0%, ${50 + percentage * 0.5}% 0%, 50% 50%)`
                    }}
                  />
                );
              })}
            </div>
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center">
                <div className="text-lg font-bold">${totalCost.toFixed(2)}</div>
                <div className="text-xs text-gray-600">Total Cost</div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="space-y-2">
          {Object.entries(data.metrics.aiCostMetrics.costByAgent).map(([agent, cost], index) => {
            const percentage = (cost / totalCost) * 100;
            return (
              <div key={agent} className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className={`w-3 h-3 rounded-full ${colors[index % colors.length]}`} />
                  <span className="text-sm capitalize">{agent.replace('-', ' ')}</span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium">${cost.toFixed(2)}</div>
                  <div className="text-xs text-gray-600">{percentage.toFixed(1)}%</div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  const renderPerformanceOverview = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Success Rate Trend</h4>
          <div className="h-24 flex items-end space-x-1">
            {trends.map((trend, index) => (
              <div key={index} className="flex-1 flex flex-col items-center">
                <div 
                  className="bg-green-500 rounded-t w-full"
                  style={{ height: `${(trend.successRate / 100) * 80}px` }}
                />
                <div className="text-xs mt-1">{trend.successRate.toFixed(0)}%</div>
              </div>
            ))}
          </div>
        </div>
        
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Execution Time Trend</h4>
          <div className="h-24 flex items-end space-x-1">
            {trends.map((trend, index) => (
              <div key={index} className="flex-1 flex flex-col items-center">
                <div 
                  className="bg-blue-500 rounded-t w-full"
                  style={{ height: `${(trend.averageTime / 300000) * 80}px` }}
                />
                <div className="text-xs mt-1">{Math.round(trend.averageTime / 1000)}s</div>
              </div>
            ))}
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-3 gap-4 text-center">
        <div className="p-3 bg-green-50 rounded-lg">
          <div className="text-lg font-bold text-green-600">
            {data.metrics.successRate.toFixed(1)}%
          </div>
          <div className="text-xs text-gray-600">Avg Success Rate</div>
        </div>
        <div className="p-3 bg-blue-50 rounded-lg">
          <div className="text-lg font-bold text-blue-600">
            {Math.round(data.metrics.averageExecutionTime / 1000)}s
          </div>
          <div className="text-xs text-gray-600">Avg Execution Time</div>
        </div>
        <div className="p-3 bg-purple-50 rounded-lg">
          <div className="text-lg font-bold text-purple-600">
            {data.metrics.totalExecutions}
          </div>
          <div className="text-xs text-gray-600">Total Executions</div>
        </div>
      </div>
    </div>
  );

  const renderAgentComparison = () => {
    const agents = Object.keys(data.metrics.aiCostMetrics.costByAgent);
    const colors = ['bg-blue-500', 'bg-green-500', 'bg-purple-500', 'bg-orange-500'];
    
    return (
      <div className="space-y-4">
        <div className="grid grid-cols-1 gap-3">
          {agents.map((agent, index) => {
            const cost = data.metrics.aiCostMetrics.costByAgent[agent];
            const maxCost = Math.max(...Object.values(data.metrics.aiCostMetrics.costByAgent));
            const percentage = (cost / maxCost) * 100;
            
            return (
              <div key={agent} className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium capitalize">
                    {agent.replace('-', ' ')}
                  </span>
                  <Badge variant="outline">${cost.toFixed(2)}</Badge>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full ${colors[index % colors.length]}`}
                    style={{ width: `${percentage}%` }}
                  />
                </div>
                <div className="flex justify-between text-xs text-gray-600">
                  <span>Performance: Good</span>
                  <span>Efficiency: {(Math.random() * 20 + 80).toFixed(0)}%</span>
                </div>
              </div>
            );
          })}
        </div>
        
        <div className="pt-4 border-t">
          <h4 className="text-sm font-medium mb-2">Agent Insights</h4>
          <div className="space-y-2 text-xs">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full" />
              <span>Most cost-effective: {agents[0]?.replace('-', ' ')}</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full" />
              <span>Highest usage: {agents[0]?.replace('-', ' ')}</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-orange-500 rounded-full" />
              <span>Optimization opportunity: Review model selection</span>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderChart = () => {
    switch (activeChart) {
      case 'execution-trends':
        return renderExecutionTrends();
      case 'cost-breakdown':
        return renderCostBreakdown();
      case 'performance-overview':
        return renderPerformanceOverview();
      case 'agent-comparison':
        return renderAgentComparison();
      default:
        return renderExecutionTrends();
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="flex items-center">
              {chartConfigs[activeChart].icon}
              <span className="ml-2">{chartConfigs[activeChart].title}</span>
            </CardTitle>
            <CardDescription>
              {chartConfigs[activeChart].description}
            </CardDescription>
          </div>
          
          <div className="flex items-center space-x-2">
            <Calendar className="h-4 w-4 text-gray-500" />
            <span className="text-sm text-gray-600">Last 7 days</span>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Chart Type Selector */}
        <div className="flex flex-wrap gap-2">
          {Object.entries(chartConfigs).map(([key, config]) => (
            <Button
              key={key}
              variant={activeChart === key ? 'default' : 'outline'}
              size="sm"
              onClick={() => setActiveChart(key as ChartType)}
              className="flex items-center"
            >
              {config.icon}
              <span className="ml-2 hidden sm:inline">{config.title}</span>
            </Button>
          ))}
        </div>

        {/* Chart Content */}
        <div className="min-h-[300px]">
          {renderChart()}
        </div>

        {/* Chart Footer */}
        <div className="flex justify-between items-center text-xs text-gray-600 pt-4 border-t">
          <span>Data refreshed every 5 minutes</span>
          <span>Interactive charts available in full dashboard</span>
        </div>
      </CardContent>
    </Card>
  );
}
