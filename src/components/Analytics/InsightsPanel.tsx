/**
 * Insights Panel Component
 * 
 * AI-powered insights and recommendations
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Alert, AlertDescription } from '../ui/alert';
import { 
  Lightbulb, 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle,
  CheckCircle,
  DollarSign,
  Zap,
  Target,
  RefreshCw
} from 'lucide-react';

interface InsightsPanelProps {
  timeRange: string;
}

interface Insight {
  type: 'performance' | 'cost' | 'quality' | 'efficiency';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  impact: string;
  recommendation: string;
  estimatedSavings?: number;
  implementationEffort: 'low' | 'medium' | 'high';
}

interface InsightsData {
  performanceInsights: Insight[];
  costOptimizations: Array<{
    type: string;
    description: string;
    potentialSavings: number;
    priority: string;
  }>;
  qualityImprovements: Array<{
    area: string;
    currentScore: number;
    targetScore: number;
    actions: string[];
  }>;
  systemHealth: {
    score: number;
    issues: string[];
    recommendations: string[];
  };
  trends: {
    direction: 'improving' | 'declining' | 'stable';
    confidence: number;
    keyFactors: string[];
  };
}

export default function InsightsPanel({ timeRange }: InsightsPanelProps) {
  const [insights, setInsights] = useState<InsightsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchInsights();
  }, [timeRange]);

  const fetchInsights = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`/api/analytics/insights?timeRange=${timeRange}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch insights');
      }
      
      const result = await response.json();
      setInsights(result.data.insights);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchInsights();
    setRefreshing(false);
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'high':
        return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      case 'medium':
        return <Lightbulb className="h-4 w-4 text-yellow-500" />;
      default:
        return <CheckCircle className="h-4 w-4 text-blue-500" />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'border-red-200 bg-red-50';
      case 'high':
        return 'border-orange-200 bg-orange-50';
      case 'medium':
        return 'border-yellow-200 bg-yellow-50';
      default:
        return 'border-blue-200 bg-blue-50';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'cost':
        return <DollarSign className="h-4 w-4" />;
      case 'performance':
        return <Zap className="h-4 w-4" />;
      case 'quality':
        return <Target className="h-4 w-4" />;
      default:
        return <Lightbulb className="h-4 w-4" />;
    }
  };

  const getTrendIcon = (direction: string) => {
    switch (direction) {
      case 'improving':
        return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'declining':
        return <TrendingDown className="h-4 w-4 text-red-500" />;
      default:
        return <div className="h-4 w-4 bg-gray-400 rounded-full" />;
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Lightbulb className="h-5 w-5 mr-2" />
            AI Insights
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-32">
            <RefreshCw className="h-6 w-6 animate-spin text-blue-500" />
            <span className="ml-2">Generating insights...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Lightbulb className="h-5 w-5 mr-2" />
            AI Insights
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Failed to load insights: {error}
            </AlertDescription>
          </Alert>
          <Button onClick={fetchInsights} className="mt-4">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (!insights) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Lightbulb className="h-5 w-5 mr-2" />
            AI Insights
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-gray-500">No insights available</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="flex items-center">
              <Lightbulb className="h-5 w-5 mr-2" />
              AI Insights & Recommendations
            </CardTitle>
            <CardDescription>
              Automated analysis and optimization suggestions
            </CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* System Health Overview */}
        <div className="p-4 bg-gray-50 rounded-lg">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium">System Health Overview</h4>
            <Badge variant={insights.systemHealth.score >= 90 ? 'default' : 'secondary'}>
              {insights.systemHealth.score}% Health Score
            </Badge>
          </div>
          
          <div className="flex items-center space-x-2 mb-2">
            {getTrendIcon(insights.trends.direction)}
            <span className="text-sm font-medium">
              Trend: {insights.trends.direction}
            </span>
            <Badge variant="outline">
              {(insights.trends.confidence * 100).toFixed(0)}% confidence
            </Badge>
          </div>
          
          {insights.systemHealth.issues.length > 0 && (
            <div className="mt-3">
              <p className="text-sm font-medium text-red-600 mb-1">Active Issues:</p>
              <ul className="text-sm text-red-600 space-y-1">
                {insights.systemHealth.issues.map((issue, index) => (
                  <li key={index} className="flex items-start space-x-2">
                    <span className="text-red-500 mt-1">•</span>
                    <span>{issue}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>

        {/* Performance Insights */}
        {insights.performanceInsights.length > 0 && (
          <div className="space-y-3">
            <h4 className="font-medium">Performance Insights</h4>
            {insights.performanceInsights.map((insight, index) => (
              <div key={index} className={`p-4 rounded-lg border ${getSeverityColor(insight.severity)}`}>
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    {getSeverityIcon(insight.severity)}
                    {getTypeIcon(insight.type)}
                    <h5 className="font-medium">{insight.title}</h5>
                  </div>
                  <Badge variant="outline" className="text-xs">
                    {insight.implementationEffort} effort
                  </Badge>
                </div>
                
                <p className="text-sm text-gray-700 mb-2">{insight.description}</p>
                <p className="text-sm text-gray-600 mb-3">
                  <strong>Impact:</strong> {insight.impact}
                </p>
                <p className="text-sm text-blue-700">
                  <strong>Recommendation:</strong> {insight.recommendation}
                </p>
                
                {insight.estimatedSavings && (
                  <div className="mt-2 flex items-center space-x-2">
                    <DollarSign className="h-4 w-4 text-green-600" />
                    <span className="text-sm font-medium text-green-600">
                      Potential savings: ${insight.estimatedSavings.toFixed(2)}
                    </span>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Cost Optimizations */}
        {insights.costOptimizations.length > 0 && (
          <div className="space-y-3">
            <h4 className="font-medium">Cost Optimization Opportunities</h4>
            {insights.costOptimizations.map((optimization, index) => (
              <div key={index} className="p-3 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex justify-between items-start mb-2">
                  <p className="font-medium text-green-800">{optimization.description}</p>
                  <Badge variant={optimization.priority === 'high' ? 'destructive' : 'default'}>
                    {optimization.priority} priority
                  </Badge>
                </div>
                <div className="flex items-center space-x-2">
                  <DollarSign className="h-4 w-4 text-green-600" />
                  <span className="text-sm font-medium text-green-600">
                    Potential savings: ${optimization.potentialSavings.toFixed(2)}
                  </span>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Quality Improvements */}
        {insights.qualityImprovements.length > 0 && (
          <div className="space-y-3">
            <h4 className="font-medium">Quality Improvement Opportunities</h4>
            {insights.qualityImprovements.map((improvement, index) => (
              <div key={index} className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex justify-between items-center mb-2">
                  <h5 className="font-medium text-blue-800">{improvement.area}</h5>
                  <div className="text-sm text-blue-600">
                    {improvement.currentScore} → {improvement.targetScore}
                  </div>
                </div>
                <div className="space-y-1">
                  {improvement.actions.map((action, actionIndex) => (
                    <div key={actionIndex} className="flex items-start space-x-2">
                      <CheckCircle className="h-3 w-3 text-blue-500 mt-1" />
                      <span className="text-sm text-blue-700">{action}</span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Key Factors */}
        <div className="space-y-3">
          <h4 className="font-medium">Key Performance Factors</h4>
          <div className="grid grid-cols-1 gap-2">
            {insights.trends.keyFactors.map((factor, index) => (
              <div key={index} className="flex items-center space-x-2 p-2 bg-gray-50 rounded">
                <Target className="h-4 w-4 text-gray-600" />
                <span className="text-sm">{factor}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Footer */}
        <div className="pt-4 border-t text-xs text-gray-600">
          <div className="flex justify-between items-center">
            <span>Insights generated using AI analysis</span>
            <span>Last updated: {new Date().toLocaleTimeString()}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
