/**
 * Performance Metrics Component
 * 
 * Displays workflow performance indicators and trends
 */

'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Progress } from '../ui/progress';
import { Badge } from '../ui/badge';
import { 
  Clock, 
  CheckCircle, 
  XCircle, 
  TrendingUp, 
  TrendingDown,
  Activity,
  Target,
  Zap
} from 'lucide-react';

interface PerformanceMetricsProps {
  data: {
    metrics: {
      totalExecutions: number;
      successRate: number;
      averageExecutionTime: number;
      stepPerformance: Array<{
        stepId: string;
        stepName: string;
        averageExecutionTime: number;
        successRate: number;
        errorRate: number;
        averageCost: number;
        bottleneckScore: number;
      }>;
      qualityMetrics: {
        averageQualityScore: number;
        improvementRate: number;
      };
    };
    realTimeMetrics: {
      activeExecutions: number;
      currentThroughput: number;
      systemHealth: number;
    };
  };
}

export default function PerformanceMetrics({ data }: PerformanceMetricsProps) {
  const getPerformanceColor = (value: number, threshold: number = 90) => {
    if (value >= threshold) return 'text-green-600';
    if (value >= threshold * 0.7) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getPerformanceBadge = (value: number, threshold: number = 90) => {
    if (value >= threshold) return 'default';
    if (value >= threshold * 0.7) return 'secondary';
    return 'destructive';
  };

  const getTrendIcon = (rate: number) => {
    if (rate > 0) return <TrendingUp className="h-4 w-4 text-green-500" />;
    if (rate < 0) return <TrendingDown className="h-4 w-4 text-red-500" />;
    return <Activity className="h-4 w-4 text-gray-500" />;
  };

  const formatTime = (milliseconds: number) => {
    if (milliseconds < 1000) return `${Math.round(milliseconds)}ms`;
    if (milliseconds < 60000) return `${(milliseconds / 1000).toFixed(1)}s`;
    return `${(milliseconds / 60000).toFixed(1)}m`;
  };

  const getBottleneckSeverity = (score: number) => {
    if (score >= 80) return { label: 'Critical', variant: 'destructive' as const };
    if (score >= 60) return { label: 'High', variant: 'default' as const };
    if (score >= 40) return { label: 'Medium', variant: 'secondary' as const };
    return { label: 'Low', variant: 'outline' as const };
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Activity className="h-5 w-5 mr-2" />
          Performance Metrics
        </CardTitle>
        <CardDescription>
          Workflow execution performance and system health indicators
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Key Performance Indicators */}
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <div className="flex items-center justify-center mb-2">
              <CheckCircle className="h-5 w-5 text-blue-600 mr-2" />
              <span className="font-medium">Success Rate</span>
            </div>
            <p className={`text-2xl font-bold ${getPerformanceColor(data.metrics.successRate)}`}>
              {data.metrics.successRate.toFixed(1)}%
            </p>
            <p className="text-xs text-gray-600 mt-1">
              {data.metrics.totalExecutions} total executions
            </p>
          </div>

          <div className="text-center p-4 bg-green-50 rounded-lg">
            <div className="flex items-center justify-center mb-2">
              <Clock className="h-5 w-5 text-green-600 mr-2" />
              <span className="font-medium">Avg Time</span>
            </div>
            <p className="text-2xl font-bold text-green-600">
              {formatTime(data.metrics.averageExecutionTime)}
            </p>
            <p className="text-xs text-gray-600 mt-1">
              Per workflow execution
            </p>
          </div>
        </div>

        {/* System Health */}
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="font-medium">System Health</span>
            <Badge variant={getPerformanceBadge(data.realTimeMetrics.systemHealth)}>
              {data.realTimeMetrics.systemHealth}%
            </Badge>
          </div>
          <Progress value={data.realTimeMetrics.systemHealth} className="h-2" />
          <div className="flex justify-between text-xs text-gray-600">
            <span>Active: {data.realTimeMetrics.activeExecutions}</span>
            <span>Throughput: {data.realTimeMetrics.currentThroughput}/min</span>
          </div>
        </div>

        {/* Quality Trends */}
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="font-medium">Quality Score</span>
            <div className="flex items-center space-x-2">
              {getTrendIcon(data.metrics.qualityMetrics.improvementRate)}
              <Badge variant={getPerformanceBadge(data.metrics.qualityMetrics.averageQualityScore, 85)}>
                {data.metrics.qualityMetrics.averageQualityScore}/100
              </Badge>
            </div>
          </div>
          <Progress value={data.metrics.qualityMetrics.averageQualityScore} className="h-2" />
          <div className="flex justify-between text-xs text-gray-600">
            <span>Target: 85+</span>
            <span className={data.metrics.qualityMetrics.improvementRate >= 0 ? 'text-green-600' : 'text-red-600'}>
              {data.metrics.qualityMetrics.improvementRate >= 0 ? '+' : ''}{data.metrics.qualityMetrics.improvementRate.toFixed(1)}% trend
            </span>
          </div>
        </div>

        {/* Step Performance Analysis */}
        {data.metrics.stepPerformance && data.metrics.stepPerformance.length > 0 && (
          <div className="space-y-4">
            <h4 className="font-medium">Step Performance Analysis</h4>
            <div className="space-y-3">
              {data.metrics.stepPerformance.map((step, index) => {
                const bottleneck = getBottleneckSeverity(step.bottleneckScore);
                return (
                  <div key={step.stepId} className="p-3 bg-gray-50 rounded-lg">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <p className="font-medium text-sm">{step.stepName}</p>
                        <p className="text-xs text-gray-600">Step {index + 1}</p>
                      </div>
                      <Badge variant={bottleneck.variant} className="text-xs">
                        {bottleneck.label} Impact
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-3 gap-3 text-xs">
                      <div>
                        <p className="text-gray-600">Avg Time</p>
                        <p className="font-medium">{formatTime(step.averageExecutionTime)}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Success Rate</p>
                        <p className={`font-medium ${getPerformanceColor(step.successRate)}`}>
                          {step.successRate.toFixed(1)}%
                        </p>
                      </div>
                      <div>
                        <p className="text-gray-600">Avg Cost</p>
                        <p className="font-medium">${step.averageCost.toFixed(2)}</p>
                      </div>
                    </div>

                    {step.errorRate > 10 && (
                      <div className="mt-2 flex items-center text-xs text-red-600">
                        <XCircle className="h-3 w-3 mr-1" />
                        High error rate: {step.errorRate.toFixed(1)}%
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Performance Recommendations */}
        <div className="space-y-3">
          <h4 className="font-medium">Performance Recommendations</h4>
          <div className="space-y-2">
            {data.metrics.successRate < 90 && (
              <div className="flex items-start space-x-2 p-2 bg-red-50 rounded-lg">
                <Target className="h-4 w-4 text-red-600 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-red-800">Improve Success Rate</p>
                  <p className="text-xs text-red-600">
                    Current rate is below 90% target. Review error patterns and add retry logic.
                  </p>
                </div>
              </div>
            )}

            {data.metrics.averageExecutionTime > 300000 && (
              <div className="flex items-start space-x-2 p-2 bg-yellow-50 rounded-lg">
                <Zap className="h-4 w-4 text-yellow-600 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-yellow-800">Optimize Execution Time</p>
                  <p className="text-xs text-yellow-600">
                    Average execution time is high. Consider parallel processing or caching.
                  </p>
                </div>
              </div>
            )}

            {data.metrics.qualityMetrics.averageQualityScore < 85 && (
              <div className="flex items-start space-x-2 p-2 bg-blue-50 rounded-lg">
                <CheckCircle className="h-4 w-4 text-blue-600 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-blue-800">Enhance Quality Controls</p>
                  <p className="text-xs text-blue-600">
                    Quality score below target. Implement additional validation steps.
                  </p>
                </div>
              </div>
            )}

            {data.realTimeMetrics.systemHealth < 90 && (
              <div className="flex items-start space-x-2 p-2 bg-orange-50 rounded-lg">
                <Activity className="h-4 w-4 text-orange-600 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-orange-800">Monitor System Health</p>
                  <p className="text-xs text-orange-600">
                    System health below optimal. Check resource utilization and error rates.
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Real-time Status */}
        <div className="pt-4 border-t">
          <div className="flex justify-between items-center text-sm">
            <span className="text-gray-600">Real-time Status</span>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                <span className="text-xs">Live</span>
              </div>
              <span className="text-xs text-gray-600">
                Last updated: {new Date().toLocaleTimeString()}
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
