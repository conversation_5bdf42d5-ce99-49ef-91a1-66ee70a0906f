/**
 * Analytics Dashboard Component
 * 
 * Main dashboard for workflow analytics with AI cost tracking
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { <PERSON><PERSON> } from '../ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Badge } from '../ui/badge';
import { Alert, AlertDescription } from '../ui/alert';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  Download,
  RefreshCw
} from 'lucide-react';
import MetricsChart from './MetricsChart';
import PerformanceMetrics from './PerformanceMetrics';
import InsightsPanel from './InsightsPanel';
import CostTracker from './CostTracker';

interface AnalyticsData {
  metrics: {
    totalExecutions: number;
    successRate: number;
    averageExecutionTime: number;
    aiCostMetrics: {
      totalCost: number;
      averageCostPerWorkflow: number;
      totalTokensUsed: number;
      tokenEfficiency: number;
      costByAgent: Record<string, number>;
    };
    qualityMetrics: {
      averageQualityScore: number;
      improvementRate: number;
    };
  };
  realTimeMetrics: {
    activeExecutions: number;
    currentThroughput: number;
    systemHealth: number;
    budgetUtilization: number;
  };
  costReport: {
    totalCost: number;
    totalExecutions: number;
    averageCostPerExecution: number;
    recommendations: Array<{
      type: string;
      description: string;
      potentialSavings: number;
      priority: string;
    }>;
  };
}

interface AnalyticsDashboardProps {
  className?: string;
}

export default function AnalyticsDashboard({ className }: AnalyticsDashboardProps) {
  const [data, setData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState('30d');
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchAnalyticsData();
  }, [timeRange]);

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`/api/analytics/metrics?timeRange=${timeRange}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch analytics data');
      }
      
      const result = await response.json();
      setData(result.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchAnalyticsData();
    setRefreshing(false);
  };

  const handleExport = async (format: 'json' | 'csv' | 'pdf') => {
    try {
      const response = await fetch(`/api/analytics/export?format=${format}&timeRange=${timeRange}`);
      
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `analytics-${timeRange}.${format}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        throw new Error('Export failed');
      }
    } catch (err) {
      console.error('Export error:', err);
      alert('Export failed. Please try again.');
    }
  };

  if (loading) {
    return (
      <div className={`p-6 ${className}`}>
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin text-blue-500" />
          <span className="ml-2 text-lg">Loading analytics...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`p-6 ${className}`}>
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Failed to load analytics data: {error}
          </AlertDescription>
        </Alert>
        <Button onClick={fetchAnalyticsData} className="mt-4">
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry
        </Button>
      </div>
    );
  }

  if (!data) {
    return (
      <div className={`p-6 ${className}`}>
        <div className="text-center text-gray-500">No analytics data available</div>
      </div>
    );
  }

  const getHealthColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getTrendIcon = (rate: number) => {
    if (rate > 0) return <TrendingUp className="h-4 w-4 text-green-500" />;
    if (rate < 0) return <TrendingDown className="h-4 w-4 text-red-500" />;
    return <div className="h-4 w-4" />;
  };

  return (
    <div className={`p-6 space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Workflow Analytics</h1>
          <p className="text-gray-600 mt-1">Performance insights and AI cost tracking</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          
          <Select onValueChange={(format) => handleExport(format as any)}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Export" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="json">JSON</SelectItem>
              <SelectItem value="csv">CSV</SelectItem>
              <SelectItem value="pdf">PDF</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Executions</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.metrics.totalExecutions}</div>
            <p className="text-xs text-muted-foreground">
              Success rate: {data.metrics.successRate.toFixed(1)}%
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">AI Costs</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${data.metrics.aiCostMetrics.totalCost.toFixed(2)}</div>
            <p className="text-xs text-muted-foreground">
              Avg per workflow: ${data.metrics.aiCostMetrics.averageCostPerWorkflow.toFixed(2)}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Execution Time</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round(data.metrics.averageExecutionTime / 1000)}s
            </div>
            <p className="text-xs text-muted-foreground">
              {data.metrics.totalExecutions} total executions
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Health</CardTitle>
            <div className={`h-4 w-4 rounded-full ${
              data.realTimeMetrics.systemHealth >= 90 ? 'bg-green-500' : 
              data.realTimeMetrics.systemHealth >= 70 ? 'bg-yellow-500' : 'bg-red-500'
            }`} />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getHealthColor(data.realTimeMetrics.systemHealth)}`}>
              {data.realTimeMetrics.systemHealth}%
            </div>
            <p className="text-xs text-muted-foreground">
              Active: {data.realTimeMetrics.activeExecutions}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quality and Cost Insights */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              Quality Metrics
              {getTrendIcon(data.metrics.qualityMetrics.improvementRate)}
            </CardTitle>
            <CardDescription>
              Content quality trends and improvements
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Average Quality Score</span>
                <Badge variant={data.metrics.qualityMetrics.averageQualityScore >= 85 ? 'default' : 'secondary'}>
                  {data.metrics.qualityMetrics.averageQualityScore}/100
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Improvement Rate</span>
                <span className={`text-sm ${data.metrics.qualityMetrics.improvementRate >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {data.metrics.qualityMetrics.improvementRate >= 0 ? '+' : ''}{data.metrics.qualityMetrics.improvementRate.toFixed(1)}%
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Cost Optimization</CardTitle>
            <CardDescription>
              AI cost recommendations and savings opportunities
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {data.costReport.recommendations.slice(0, 3).map((rec, index) => (
                <div key={index} className="flex justify-between items-start">
                  <div className="flex-1">
                    <p className="text-sm font-medium">{rec.description}</p>
                    <Badge variant="outline" className="mt-1">
                      {rec.priority} priority
                    </Badge>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-bold text-green-600">
                      ${rec.potentialSavings.toFixed(2)}
                    </p>
                    <p className="text-xs text-muted-foreground">potential savings</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Components */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <CostTracker data={data} />
        <PerformanceMetrics data={data} />
      </div>

      <div className="grid grid-cols-1 gap-6">
        <MetricsChart data={data} />
        <InsightsPanel timeRange={timeRange} />
      </div>
    </div>
  );
}
