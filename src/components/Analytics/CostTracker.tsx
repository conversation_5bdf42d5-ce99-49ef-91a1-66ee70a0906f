/**
 * Cost Tracker Component
 * 
 * Real-time AI cost tracking and budget monitoring
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Progress } from '../ui/progress';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Alert, AlertDescription } from '../ui/alert';
import { 
  DollarSign, 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle,
  Target,
  Zap
} from 'lucide-react';

interface CostTrackerProps {
  data: {
    metrics: {
      aiCostMetrics: {
        totalCost: number;
        averageCostPerWorkflow: number;
        totalTokensUsed: number;
        tokenEfficiency: number;
        costByAgent: Record<string, number>;
      };
    };
    costReport: {
      totalCost: number;
      averageCostPerExecution: number;
      recommendations: Array<{
        type: string;
        description: string;
        potentialSavings: number;
        priority: string;
      }>;
    };
    realTimeMetrics: {
      budgetUtilization: number;
    };
  };
}

interface BudgetAlert {
  type: 'warning' | 'critical' | 'info';
  message: string;
  threshold: number;
}

export default function CostTracker({ data }: CostTrackerProps) {
  const [budget, setBudget] = useState(100); // Default $100 budget
  const [alerts, setAlerts] = useState<BudgetAlert[]>([]);
  const [showBudgetSettings, setShowBudgetSettings] = useState(false);

  useEffect(() => {
    checkBudgetAlerts();
  }, [data, budget]);

  const checkBudgetAlerts = () => {
    const newAlerts: BudgetAlert[] = [];
    const utilization = (data.metrics.aiCostMetrics.totalCost / budget) * 100;

    if (utilization >= 90) {
      newAlerts.push({
        type: 'critical',
        message: 'Budget almost exceeded! Consider implementing cost optimizations.',
        threshold: 90
      });
    } else if (utilization >= 75) {
      newAlerts.push({
        type: 'warning',
        message: 'Budget utilization is high. Monitor costs closely.',
        threshold: 75
      });
    } else if (utilization >= 50) {
      newAlerts.push({
        type: 'info',
        message: 'Budget utilization is on track.',
        threshold: 50
      });
    }

    setAlerts(newAlerts);
  };

  const budgetUtilization = (data.metrics.aiCostMetrics.totalCost / budget) * 100;
  const remainingBudget = budget - data.metrics.aiCostMetrics.totalCost;
  const projectedMonthlySpend = data.metrics.aiCostMetrics.totalCost * (30 / 7); // Rough projection

  const getUtilizationColor = (utilization: number) => {
    if (utilization >= 90) return 'bg-red-500';
    if (utilization >= 75) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const getAgentColor = (index: number) => {
    const colors = ['bg-blue-500', 'bg-green-500', 'bg-purple-500', 'bg-orange-500', 'bg-pink-500'];
    return colors[index % colors.length];
  };

  const totalPotentialSavings = data.costReport.recommendations.reduce(
    (sum, rec) => sum + rec.potentialSavings, 0
  );

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="flex items-center">
              <DollarSign className="h-5 w-5 mr-2" />
              AI Cost Tracking
            </CardTitle>
            <CardDescription>
              Real-time cost monitoring and budget management
            </CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowBudgetSettings(!showBudgetSettings)}
          >
            <Target className="h-4 w-4 mr-2" />
            Budget Settings
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Budget Settings */}
        {showBudgetSettings && (
          <div className="p-4 bg-gray-50 rounded-lg">
            <h4 className="font-medium mb-2">Budget Configuration</h4>
            <div className="flex items-center space-x-4">
              <label className="text-sm font-medium">Monthly Budget:</label>
              <input
                type="number"
                value={budget}
                onChange={(e) => setBudget(Number(e.target.value))}
                className="px-3 py-1 border rounded-md w-24"
                min="0"
                step="10"
              />
              <span className="text-sm text-gray-600">USD</span>
            </div>
          </div>
        )}

        {/* Budget Alerts */}
        {alerts.map((alert, index) => (
          <Alert key={index} variant={alert.type === 'critical' ? 'destructive' : 'default'}>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{alert.message}</AlertDescription>
          </Alert>
        ))}

        {/* Budget Overview */}
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">Budget Utilization</span>
            <span className="text-sm text-gray-600">
              ${data.metrics.aiCostMetrics.totalCost.toFixed(2)} / ${budget.toFixed(2)}
            </span>
          </div>
          
          <Progress 
            value={Math.min(budgetUtilization, 100)} 
            className="h-3"
          />
          
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <p className="text-2xl font-bold text-green-600">
                ${remainingBudget.toFixed(2)}
              </p>
              <p className="text-xs text-gray-600">Remaining</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-blue-600">
                {budgetUtilization.toFixed(1)}%
              </p>
              <p className="text-xs text-gray-600">Utilized</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-orange-600">
                ${projectedMonthlySpend.toFixed(2)}
              </p>
              <p className="text-xs text-gray-600">Projected</p>
            </div>
          </div>
        </div>

        {/* Cost Breakdown by Agent */}
        <div className="space-y-4">
          <h4 className="font-medium">Cost by Agent Type</h4>
          <div className="space-y-3">
            {Object.entries(data.metrics.aiCostMetrics.costByAgent).map(([agent, cost], index) => {
              const percentage = (cost / data.metrics.aiCostMetrics.totalCost) * 100;
              return (
                <div key={agent} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-3 h-3 rounded-full ${getAgentColor(index)}`} />
                    <span className="text-sm font-medium capitalize">
                      {agent.replace('-', ' ')}
                    </span>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-bold">${cost.toFixed(2)}</p>
                    <p className="text-xs text-gray-600">{percentage.toFixed(1)}%</p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Token Efficiency */}
        <div className="space-y-4">
          <h4 className="font-medium">Token Efficiency</h4>
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <p className="text-lg font-bold text-blue-600">
                {data.metrics.aiCostMetrics.totalTokensUsed.toLocaleString()}
              </p>
              <p className="text-xs text-gray-600">Total Tokens</p>
            </div>
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <p className="text-lg font-bold text-green-600">
                ${(data.metrics.aiCostMetrics.tokenEfficiency * 1000).toFixed(3)}
              </p>
              <p className="text-xs text-gray-600">Cost per 1K Tokens</p>
            </div>
          </div>
        </div>

        {/* Cost Optimization Opportunities */}
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h4 className="font-medium">Optimization Opportunities</h4>
            <Badge variant="outline" className="text-green-600">
              <Zap className="h-3 w-3 mr-1" />
              ${totalPotentialSavings.toFixed(2)} potential savings
            </Badge>
          </div>
          
          <div className="space-y-2">
            {data.costReport.recommendations.slice(0, 3).map((rec, index) => (
              <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <div className="flex-1">
                  <p className="text-sm font-medium">{rec.description}</p>
                  <Badge 
                    variant={rec.priority === 'high' ? 'destructive' : rec.priority === 'medium' ? 'default' : 'secondary'}
                    className="mt-1"
                  >
                    {rec.priority} priority
                  </Badge>
                </div>
                <div className="text-right ml-4">
                  <p className="text-sm font-bold text-green-600">
                    ${rec.potentialSavings.toFixed(2)}
                  </p>
                  <p className="text-xs text-gray-600">savings</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="flex space-x-2">
          <Button variant="outline" size="sm" className="flex-1">
            <TrendingDown className="h-4 w-4 mr-2" />
            Optimize Costs
          </Button>
          <Button variant="outline" size="sm" className="flex-1">
            <Target className="h-4 w-4 mr-2" />
            Set Alerts
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
