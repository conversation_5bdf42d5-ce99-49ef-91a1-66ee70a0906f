/**
 * Quality Validator Component
 * 
 * Main content input and validation interface
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Alert, AlertDescription } from '../ui/alert';
import { 
  Play, 
  RefreshCw, 
  Settings, 
  FileText,
  Target,
  BarChart3,
  CheckCircle
} from 'lucide-react';

interface QualityValidatorProps {
  content: string;
  onContentChange: (content: string) => void;
  onValidate: (content: string, config?: any) => void;
  loading: boolean;
}

interface ValidationConfig {
  seoConfig: {
    targetKeywords: string[];
    minWordCount: number;
    maxWordCount: number;
  };
  readabilityTarget: {
    gradeLevel: number;
    fleschScore: number;
  };
  qualityThresholds: {
    minimum: number;
    target: number;
    excellent: number;
  };
  brandGuidelines?: {
    tone: string;
    prohibitedWords: string[];
    brandTerms: string[];
  };
}

export default function QualityValidator({ 
  content, 
  onContentChange, 
  onValidate, 
  loading 
}: QualityValidatorProps) {
  const [showConfig, setShowConfig] = useState(false);
  const [config, setConfig] = useState<ValidationConfig>({
    seoConfig: {
      targetKeywords: [],
      minWordCount: 300,
      maxWordCount: 2000
    },
    readabilityTarget: {
      gradeLevel: 8,
      fleschScore: 70
    },
    qualityThresholds: {
      minimum: 60,
      target: 80,
      excellent: 90
    }
  });

  const [keywordInput, setKeywordInput] = useState('');
  const [prohibitedWordsInput, setProhibitedWordsInput] = useState('');
  const [brandTermsInput, setBrandTermsInput] = useState('');

  const handleValidate = () => {
    if (!content.trim()) {
      alert('Please enter content to validate');
      return;
    }

    if (content.length < 10) {
      alert('Content is too short for meaningful analysis');
      return;
    }

    onValidate(content, config);
  };

  const handleAddKeywords = () => {
    if (keywordInput.trim()) {
      const keywords = keywordInput.split(',').map(k => k.trim()).filter(k => k);
      setConfig(prev => ({
        ...prev,
        seoConfig: {
          ...prev.seoConfig,
          targetKeywords: [...prev.seoConfig.targetKeywords, ...keywords]
        }
      }));
      setKeywordInput('');
    }
  };

  const handleRemoveKeyword = (keyword: string) => {
    setConfig(prev => ({
      ...prev,
      seoConfig: {
        ...prev.seoConfig,
        targetKeywords: prev.seoConfig.targetKeywords.filter(k => k !== keyword)
      }
    }));
  };

  const handleAddProhibitedWords = () => {
    if (prohibitedWordsInput.trim()) {
      const words = prohibitedWordsInput.split(',').map(w => w.trim()).filter(w => w);
      setConfig(prev => ({
        ...prev,
        brandGuidelines: {
          ...prev.brandGuidelines,
          tone: prev.brandGuidelines?.tone || 'professional',
          prohibitedWords: [...(prev.brandGuidelines?.prohibitedWords || []), ...words],
          brandTerms: prev.brandGuidelines?.brandTerms || []
        }
      }));
      setProhibitedWordsInput('');
    }
  };

  const handleAddBrandTerms = () => {
    if (brandTermsInput.trim()) {
      const terms = brandTermsInput.split(',').map(t => t.trim()).filter(t => t);
      setConfig(prev => ({
        ...prev,
        brandGuidelines: {
          ...prev.brandGuidelines,
          tone: prev.brandGuidelines?.tone || 'professional',
          prohibitedWords: prev.brandGuidelines?.prohibitedWords || [],
          brandTerms: [...(prev.brandGuidelines?.brandTerms || []), ...terms]
        }
      }));
      setBrandTermsInput('');
    }
  };

  const wordCount = content.split(/\s+/).filter(word => word.length > 0).length;
  const characterCount = content.length;

  return (
    <div className="space-y-6">
      {/* Content Input */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="flex items-center">
                <FileText className="h-5 w-5 mr-2" />
                Content Input
              </CardTitle>
              <CardDescription>
                Enter or paste your content for quality validation
              </CardDescription>
            </div>
            <Button
              variant="outline"
              onClick={() => setShowConfig(!showConfig)}
            >
              <Settings className="h-4 w-4 mr-2" />
              Configure
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <textarea
              value={content}
              onChange={(e) => onContentChange(e.target.value)}
              placeholder="Enter your content here for quality analysis..."
              className="w-full h-64 p-4 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          <div className="flex justify-between items-center">
            <div className="flex space-x-4 text-sm text-gray-600">
              <span>Words: {wordCount}</span>
              <span>Characters: {characterCount}</span>
              <span>
                Status: 
                <Badge 
                  variant={wordCount >= config.seoConfig.minWordCount ? 'default' : 'secondary'}
                  className="ml-1"
                >
                  {wordCount >= config.seoConfig.minWordCount ? 'Ready' : 'Too Short'}
                </Badge>
              </span>
            </div>
            
            <Button
              onClick={handleValidate}
              disabled={loading || !content.trim()}
              className="flex items-center"
            >
              {loading ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Play className="h-4 w-4 mr-2" />
              )}
              {loading ? 'Validating...' : 'Validate Content'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Configuration Panel */}
      {showConfig && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              Validation Configuration
            </CardTitle>
            <CardDescription>
              Customize validation parameters and thresholds
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* SEO Configuration */}
            <div className="space-y-4">
              <h4 className="font-medium flex items-center">
                <BarChart3 className="h-4 w-4 mr-2" />
                SEO Configuration
              </h4>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Min Word Count</label>
                  <input
                    type="number"
                    value={config.seoConfig.minWordCount}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      seoConfig: { ...prev.seoConfig, minWordCount: Number(e.target.value) }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Max Word Count</label>
                  <input
                    type="number"
                    value={config.seoConfig.maxWordCount}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      seoConfig: { ...prev.seoConfig, maxWordCount: Number(e.target.value) }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Target Keywords</label>
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={keywordInput}
                    onChange={(e) => setKeywordInput(e.target.value)}
                    placeholder="Enter keywords separated by commas"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md"
                  />
                  <Button onClick={handleAddKeywords} variant="outline">Add</Button>
                </div>
                <div className="flex flex-wrap gap-2 mt-2">
                  {config.seoConfig.targetKeywords.map((keyword, index) => (
                    <Badge key={index} variant="secondary" className="cursor-pointer" onClick={() => handleRemoveKeyword(keyword)}>
                      {keyword} ×
                    </Badge>
                  ))}
                </div>
              </div>
            </div>

            {/* Readability Configuration */}
            <div className="space-y-4">
              <h4 className="font-medium flex items-center">
                <FileText className="h-4 w-4 mr-2" />
                Readability Targets
              </h4>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Target Grade Level</label>
                  <input
                    type="number"
                    value={config.readabilityTarget.gradeLevel}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      readabilityTarget: { ...prev.readabilityTarget, gradeLevel: Number(e.target.value) }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Target Flesch Score</label>
                  <input
                    type="number"
                    value={config.readabilityTarget.fleschScore}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      readabilityTarget: { ...prev.readabilityTarget, fleschScore: Number(e.target.value) }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  />
                </div>
              </div>
            </div>

            {/* Quality Thresholds */}
            <div className="space-y-4">
              <h4 className="font-medium flex items-center">
                <Target className="h-4 w-4 mr-2" />
                Quality Thresholds
              </h4>
              
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Minimum</label>
                  <input
                    type="number"
                    value={config.qualityThresholds.minimum}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      qualityThresholds: { ...prev.qualityThresholds, minimum: Number(e.target.value) }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Target</label>
                  <input
                    type="number"
                    value={config.qualityThresholds.target}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      qualityThresholds: { ...prev.qualityThresholds, target: Number(e.target.value) }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Excellent</label>
                  <input
                    type="number"
                    value={config.qualityThresholds.excellent}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      qualityThresholds: { ...prev.qualityThresholds, excellent: Number(e.target.value) }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  />
                </div>
              </div>
            </div>

            {/* Brand Guidelines */}
            <div className="space-y-4">
              <h4 className="font-medium flex items-center">
                <CheckCircle className="h-4 w-4 mr-2" />
                Brand Guidelines (Optional)
              </h4>
              
              <div>
                <label className="block text-sm font-medium mb-1">Brand Tone</label>
                <select
                  value={config.brandGuidelines?.tone || 'professional'}
                  onChange={(e) => setConfig(prev => ({
                    ...prev,
                    brandGuidelines: {
                      ...prev.brandGuidelines,
                      tone: e.target.value,
                      prohibitedWords: prev.brandGuidelines?.prohibitedWords || [],
                      brandTerms: prev.brandGuidelines?.brandTerms || []
                    }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                >
                  <option value="professional">Professional</option>
                  <option value="casual">Casual</option>
                  <option value="friendly">Friendly</option>
                  <option value="authoritative">Authoritative</option>
                  <option value="conversational">Conversational</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Prohibited Words</label>
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={prohibitedWordsInput}
                    onChange={(e) => setProhibitedWordsInput(e.target.value)}
                    placeholder="Enter prohibited words separated by commas"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md"
                  />
                  <Button onClick={handleAddProhibitedWords} variant="outline">Add</Button>
                </div>
                <div className="flex flex-wrap gap-2 mt-2">
                  {config.brandGuidelines?.prohibitedWords?.map((word, index) => (
                    <Badge key={index} variant="destructive" className="text-xs">
                      {word}
                    </Badge>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Brand Terms</label>
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={brandTermsInput}
                    onChange={(e) => setBrandTermsInput(e.target.value)}
                    placeholder="Enter brand terms separated by commas"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md"
                  />
                  <Button onClick={handleAddBrandTerms} variant="outline">Add</Button>
                </div>
                <div className="flex flex-wrap gap-2 mt-2">
                  {config.brandGuidelines?.brandTerms?.map((term, index) => (
                    <Badge key={index} variant="default" className="text-xs">
                      {term}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Common validation scenarios and presets
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button
              variant="outline"
              onClick={() => onContentChange("This is a sample blog post about artificial intelligence and machine learning. AI has revolutionized many industries and continues to grow rapidly. Machine learning algorithms can process vast amounts of data to identify patterns and make predictions. Companies are investing heavily in AI research and development to stay competitive in the market.")}
              className="h-auto p-4 text-left"
            >
              <div>
                <div className="font-medium">Sample Blog Post</div>
                <div className="text-xs text-gray-600">Load example content</div>
              </div>
            </Button>
            
            <Button
              variant="outline"
              onClick={() => setConfig({
                seoConfig: { targetKeywords: ['SEO', 'content', 'optimization'], minWordCount: 500, maxWordCount: 1500 },
                readabilityTarget: { gradeLevel: 6, fleschScore: 80 },
                qualityThresholds: { minimum: 70, target: 85, excellent: 95 }
              })}
              className="h-auto p-4 text-left"
            >
              <div>
                <div className="font-medium">SEO Focused</div>
                <div className="text-xs text-gray-600">Optimize for search</div>
              </div>
            </Button>
            
            <Button
              variant="outline"
              onClick={() => setConfig({
                seoConfig: { targetKeywords: [], minWordCount: 200, maxWordCount: 800 },
                readabilityTarget: { gradeLevel: 5, fleschScore: 85 },
                qualityThresholds: { minimum: 75, target: 88, excellent: 95 }
              })}
              className="h-auto p-4 text-left"
            >
              <div>
                <div className="font-medium">Easy Reading</div>
                <div className="text-xs text-gray-600">General audience</div>
              </div>
            </Button>
            
            <Button
              variant="outline"
              onClick={() => setConfig({
                seoConfig: { targetKeywords: [], minWordCount: 800, maxWordCount: 3000 },
                readabilityTarget: { gradeLevel: 12, fleschScore: 50 },
                qualityThresholds: { minimum: 80, target: 90, excellent: 98 }
              })}
              className="h-auto p-4 text-left"
            >
              <div>
                <div className="font-medium">Academic</div>
                <div className="text-xs text-gray-600">Scholarly content</div>
              </div>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
