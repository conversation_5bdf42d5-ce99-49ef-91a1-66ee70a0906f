/**
 * Quality Dashboard Component
 * 
 * Main dashboard for content quality validation and analysis
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Alert, AlertDescription } from '../ui/alert';
import { 
  CheckCircle, 
  AlertTriangle, 
  FileText, 
  BarChart3,
  Target,
  Zap,
  RefreshCw,
  Upload
} from 'lucide-react';
import QualityValidator from './QualityValidator';
import ReadabilityAnalyzer from './ReadabilityAnalyzer';
import BrandComplianceChecker from './BrandComplianceChecker';
import QualityScoreCard from './QualityScoreCard';

interface QualityDashboardProps {
  className?: string;
}

interface QualityResults {
  overallScore: number;
  qualityGrade: string;
  validation: any;
  readability: any;
  brandCompliance: any;
  qualityScore: any;
  summary: any;
  recommendations: any;
  performance: any;
}

export default function QualityDashboard({ className }: QualityDashboardProps) {
  const [content, setContent] = useState('');
  const [results, setResults] = useState<QualityResults | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'validator' | 'readability' | 'brand' | 'results'>('validator');

  const handleValidateContent = async (contentToValidate: string, config?: any) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('/api/quality/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: contentToValidate,
          config: config || {
            seoConfig: {
              targetKeywords: [],
              minWordCount: 300,
              maxWordCount: 2000
            },
            readabilityTarget: {
              gradeLevel: 8,
              fleschScore: 70
            },
            qualityThresholds: {
              minimum: 60,
              target: 80,
              excellent: 90
            }
          }
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to validate content');
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Validation failed');
      }

      setResults(result.data);
      setActiveTab('results');
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const text = e.target?.result as string;
        setContent(text);
      };
      reader.readAsText(file);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 80) return 'text-blue-600';
    if (score >= 70) return 'text-yellow-600';
    if (score >= 60) return 'text-orange-600';
    return 'text-red-600';
  };

  const getScoreBadgeVariant = (score: number) => {
    if (score >= 90) return 'default';
    if (score >= 70) return 'secondary';
    return 'destructive';
  };

  return (
    <div className={`p-6 space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Content Quality Validation</h1>
          <p className="text-gray-600 mt-1">Comprehensive content analysis and quality assessment</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <input
            type="file"
            accept=".txt,.md,.html"
            onChange={handleFileUpload}
            className="hidden"
            id="file-upload"
          />
          <label htmlFor="file-upload">
            <Button variant="outline" className="cursor-pointer">
              <Upload className="h-4 w-4 mr-2" />
              Upload File
            </Button>
          </label>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Results Summary */}
      {results && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Overall Score</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${getScoreColor(results.overallScore)}`}>
                {results.overallScore}/100
              </div>
              <Badge variant={getScoreBadgeVariant(results.overallScore)} className="mt-2">
                {results.qualityGrade}
              </Badge>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Readability</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${getScoreColor(results.readability.score)}`}>
                {results.readability.score}/100
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                {results.readability.metrics.readabilityGrade}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">SEO Score</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${getScoreColor(results.summary.seoScore)}`}>
                {results.summary.seoScore}/100
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                {results.summary.wordCount} words
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Grammar</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${getScoreColor(results.summary.grammarScore)}`}>
                {results.summary.grammarScore}/100
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                {results.validation.breakdown.grammar.errors.length} issues
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
        {[
          { id: 'validator', label: 'Content Validator', icon: <Zap className="h-4 w-4" /> },
          { id: 'readability', label: 'Readability', icon: <FileText className="h-4 w-4" /> },
          { id: 'brand', label: 'Brand Compliance', icon: <Target className="h-4 w-4" /> },
          { id: 'results', label: 'Results', icon: <BarChart3 className="h-4 w-4" /> }
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === tab.id
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            {tab.icon}
            <span>{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="min-h-[600px]">
        {activeTab === 'validator' && (
          <QualityValidator
            content={content}
            onContentChange={setContent}
            onValidate={handleValidateContent}
            loading={loading}
          />
        )}

        {activeTab === 'readability' && (
          <ReadabilityAnalyzer
            content={content}
            onContentChange={setContent}
            results={results?.readability}
          />
        )}

        {activeTab === 'brand' && (
          <BrandComplianceChecker
            content={content}
            onContentChange={setContent}
            results={results?.brandCompliance}
          />
        )}

        {activeTab === 'results' && results && (
          <div className="space-y-6">
            <QualityScoreCard results={results} />
            
            {/* Recommendations */}
            <Card>
              <CardHeader>
                <CardTitle>Recommendations</CardTitle>
                <CardDescription>
                  Actionable suggestions to improve content quality
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Critical Recommendations */}
                {results.recommendations.critical.length > 0 && (
                  <div>
                    <h4 className="font-medium text-red-600 mb-2">Critical Issues</h4>
                    <div className="space-y-2">
                      {results.recommendations.critical.map((rec: any, index: number) => (
                        <div key={index} className="p-3 bg-red-50 border border-red-200 rounded-lg">
                          <p className="font-medium text-red-800">{rec.message}</p>
                          <p className="text-sm text-red-600 mt-1">{rec.action}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Important Recommendations */}
                {results.recommendations.important.length > 0 && (
                  <div>
                    <h4 className="font-medium text-orange-600 mb-2">Important Improvements</h4>
                    <div className="space-y-2">
                      {results.recommendations.important.map((rec: any, index: number) => (
                        <div key={index} className="p-3 bg-orange-50 border border-orange-200 rounded-lg">
                          <p className="font-medium text-orange-800">{rec.message}</p>
                          <p className="text-sm text-orange-600 mt-1">{rec.action}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Suggestions */}
                {results.recommendations.suggestions.length > 0 && (
                  <div>
                    <h4 className="font-medium text-blue-600 mb-2">Suggestions</h4>
                    <div className="space-y-2">
                      {results.recommendations.suggestions.map((rec: any, index: number) => (
                        <div key={index} className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                          <p className="font-medium text-blue-800">{rec.message}</p>
                          <p className="text-sm text-blue-600 mt-1">{rec.action}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Performance Metrics */}
            <Card>
              <CardHeader>
                <CardTitle>Analysis Performance</CardTitle>
                <CardDescription>
                  Validation execution metrics and costs
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-blue-600">
                      {results.performance.executionTime}ms
                    </p>
                    <p className="text-sm text-gray-600">Execution Time</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-green-600">
                      ${results.performance.aiCosts?.totalCost.toFixed(3) || '0.000'}
                    </p>
                    <p className="text-sm text-gray-600">AI Costs</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-purple-600">
                      {Object.keys(results.performance.aiCosts?.costByValidator || {}).length}
                    </p>
                    <p className="text-sm text-gray-600">Validators Used</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
}
