/**
 * Analytics Insights API
 * GET /api/analytics/insights - Get AI-powered insights and recommendations
 */

import { NextRequest, NextResponse } from 'next/server';
import { getStateStore } from '../../../../core/workflow/singleton';
import { WorkflowAnalyticsEngine, CostOptimizationRecommendation } from '../../../../core/analytics/workflow-analytics-engine';
import { AIModelManager } from '../../../../core/ai/model-manager';

// Initialize AI Model Manager
const aiModelManager = new AIModelManager({
  defaultProvider: 'openai',
  defaultModel: 'gpt-3.5-turbo',
  providers: {
    openai: {
      apiKey: process.env.OPENAI_API_KEY || ''
    },
    anthropic: {
      apiKey: process.env.ANTHROPIC_API_KEY || ''
    }
  },
  costTracking: true,
  rateLimiting: {
    enabled: false,
    requestsPerMinute: 60,
    tokensPerMinute: 100000
  }
});

interface PerformanceInsight {
  type: 'performance' | 'cost' | 'quality' | 'efficiency';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  impact: string;
  recommendation: string;
  estimatedSavings?: number;
  implementationEffort: 'low' | 'medium' | 'high';
}

interface WorkflowInsights {
  performanceInsights: PerformanceInsight[];
  costOptimizations: CostOptimizationRecommendation[];
  qualityImprovements: Array<{
    area: string;
    currentScore: number;
    targetScore: number;
    actions: string[];
  }>;
  systemHealth: {
    score: number;
    issues: string[];
    recommendations: string[];
  };
  trends: {
    direction: 'improving' | 'declining' | 'stable';
    confidence: number;
    keyFactors: string[];
  };
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const executionId = searchParams.get('executionId');
    const timeRange = searchParams.get('timeRange') || '30d';
    const focusArea = searchParams.get('focusArea'); // 'cost', 'performance', 'quality'

    const stateStore = getStateStore();
    const analyticsEngine = new WorkflowAnalyticsEngine(stateStore, aiModelManager);

    // Generate insights based on focus area
    let insights: WorkflowInsights;

    if (executionId) {
      // Generate insights for specific execution
      insights = await generateExecutionInsights(executionId, analyticsEngine);
    } else {
      // Generate general insights
      insights = await generateGeneralInsights(timeRange, focusArea, analyticsEngine);
    }

    return NextResponse.json({
      success: true,
      data: {
        insights,
        executionId,
        timeRange,
        focusArea,
        generatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Analytics insights error:', error);
    return NextResponse.json(
      {
        error: 'Failed to generate insights',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

async function generateExecutionInsights(
  executionId: string,
  analyticsEngine: WorkflowAnalyticsEngine
): Promise<WorkflowInsights> {
  const costTracker = analyticsEngine.getCostTracker();
  const costBreakdown = await costTracker.calculateWorkflowCost(executionId);

  const performanceInsights: PerformanceInsight[] = [];
  
  // Cost-based insights
  if (costBreakdown.totalCost > 2.0) {
    performanceInsights.push({
      type: 'cost',
      severity: 'high',
      title: 'High Execution Cost Detected',
      description: `This workflow execution cost $${costBreakdown.totalCost.toFixed(2)}, which is above the recommended threshold.`,
      impact: 'Increased operational costs and reduced ROI',
      recommendation: 'Consider using more cost-effective models or optimizing prompt length',
      estimatedSavings: costBreakdown.totalCost * 0.3,
      implementationEffort: 'medium'
    });
  }

  // Efficiency insights
  if (costBreakdown.efficiency > 0.01) {
    performanceInsights.push({
      type: 'efficiency',
      severity: 'medium',
      title: 'Token Efficiency Could Be Improved',
      description: 'The cost per token ratio suggests there may be room for optimization.',
      impact: 'Suboptimal resource utilization',
      recommendation: 'Review prompt engineering and consider response caching',
      implementationEffort: 'low'
    });
  }

  return {
    performanceInsights,
    costOptimizations: [{
      type: 'model_selection',
      description: 'Switch to GPT-3.5-turbo for routine content generation tasks',
      potentialSavings: costBreakdown.totalCost * 0.4,
      implementation: 'Update model selection criteria in workflow configuration',
      priority: 'medium'
    }],
    qualityImprovements: [{
      area: 'Content Quality',
      currentScore: 85,
      targetScore: 90,
      actions: ['Implement additional quality validation steps', 'Add human review checkpoints']
    }],
    systemHealth: {
      score: 95,
      issues: [],
      recommendations: ['Monitor cost trends closely', 'Set up budget alerts']
    },
    trends: {
      direction: 'stable',
      confidence: 0.8,
      keyFactors: ['Consistent execution patterns', 'Stable cost per execution']
    }
  };
}

async function generateGeneralInsights(
  timeRange: string,
  focusArea: string | null,
  analyticsEngine: WorkflowAnalyticsEngine
): Promise<WorkflowInsights> {
  const metrics = await analyticsEngine.generateMetrics(timeRange);
  const costTracker = analyticsEngine.getCostTracker();
  const costReport = await costTracker.generateCostReport(timeRange);

  const performanceInsights: PerformanceInsight[] = [];

  // Success rate insights
  if (metrics.successRate < 90) {
    performanceInsights.push({
      type: 'performance',
      severity: metrics.successRate < 70 ? 'critical' : 'high',
      title: 'Low Workflow Success Rate',
      description: `Current success rate is ${metrics.successRate.toFixed(1)}%, below the recommended 90% threshold.`,
      impact: 'Reduced productivity and increased manual intervention',
      recommendation: 'Investigate common failure patterns and improve error handling',
      implementationEffort: 'high'
    });
  }

  // Cost insights
  if (costReport.totalCost > 100) {
    performanceInsights.push({
      type: 'cost',
      severity: 'medium',
      title: 'High Monthly AI Costs',
      description: `Total AI costs for the period: $${costReport.totalCost.toFixed(2)}`,
      impact: 'Significant operational expenses',
      recommendation: 'Implement cost optimization strategies and budget controls',
      estimatedSavings: costReport.totalCost * 0.25,
      implementationEffort: 'medium'
    });
  }

  // Quality insights
  if (metrics.qualityMetrics.averageQualityScore < 80) {
    performanceInsights.push({
      type: 'quality',
      severity: 'medium',
      title: 'Content Quality Below Target',
      description: `Average quality score is ${metrics.qualityMetrics.averageQualityScore}, below the target of 85.`,
      impact: 'Potential impact on content effectiveness and user satisfaction',
      recommendation: 'Enhance quality validation processes and review criteria',
      implementationEffort: 'medium'
    });
  }

  return {
    performanceInsights,
    costOptimizations: costReport.recommendations,
    qualityImprovements: [{
      area: 'Overall Quality',
      currentScore: metrics.qualityMetrics.averageQualityScore,
      targetScore: 90,
      actions: [
        'Implement automated quality checks',
        'Add more comprehensive review criteria',
        'Provide feedback training for reviewers'
      ]
    }],
    systemHealth: {
      score: calculateSystemHealthScore(metrics),
      issues: identifySystemIssues(metrics),
      recommendations: generateSystemRecommendations(metrics)
    },
    trends: {
      direction: determineTrendDirection(metrics),
      confidence: 0.75,
      keyFactors: [
        'Execution volume trends',
        'Cost efficiency patterns',
        'Quality score evolution'
      ]
    }
  };
}

function calculateSystemHealthScore(metrics: any): number {
  let score = 100;
  
  if (metrics.successRate < 90) score -= 20;
  if (metrics.successRate < 70) score -= 30;
  if (metrics.qualityMetrics.averageQualityScore < 80) score -= 15;
  if (metrics.aiCostMetrics.totalCost > 100) score -= 10;
  
  return Math.max(score, 0);
}

function identifySystemIssues(metrics: any): string[] {
  const issues: string[] = [];
  
  if (metrics.successRate < 90) {
    issues.push('Low workflow success rate detected');
  }
  
  if (metrics.qualityMetrics.averageQualityScore < 80) {
    issues.push('Content quality below target threshold');
  }
  
  if (metrics.aiCostMetrics.totalCost > 100) {
    issues.push('High AI operational costs');
  }
  
  return issues;
}

function generateSystemRecommendations(metrics: any): string[] {
  const recommendations: string[] = [];
  
  recommendations.push('Set up automated monitoring and alerts');
  recommendations.push('Implement cost budget controls');
  recommendations.push('Regular quality audits and improvements');
  
  if (metrics.successRate < 90) {
    recommendations.push('Investigate and resolve common failure patterns');
  }
  
  return recommendations;
}

function determineTrendDirection(metrics: any): 'improving' | 'declining' | 'stable' {
  // Simple heuristic - in a real implementation, this would analyze historical data
  if (metrics.successRate > 95 && metrics.qualityMetrics.averageQualityScore > 85) {
    return 'improving';
  } else if (metrics.successRate < 80 || metrics.qualityMetrics.averageQualityScore < 75) {
    return 'declining';
  }
  return 'stable';
}
