/**
 * Analytics Metrics API
 * GET /api/analytics/metrics - Get workflow analytics metrics
 */

import { NextRequest, NextResponse } from 'next/server';
import { getStateStore } from '../../../../core/workflow/singleton';
import { WorkflowAnalyticsEngine, AnalyticsFilters } from '../../../../core/analytics/workflow-analytics-engine';
import { AIModelManager } from '../../../../core/ai/model-manager';

// Initialize AI Model Manager with default config
const aiModelManager = new AIModelManager({
  defaultProvider: 'openai',
  defaultModel: 'gpt-3.5-turbo',
  providers: {
    openai: {
      apiKey: process.env.OPENAI_API_KEY || ''
    },
    anthropic: {
      apiKey: process.env.ANTHROPIC_API_KEY || ''
    }
  },
  costTracking: true,
  rateLimiting: {
    enabled: false,
    requestsPerMinute: 60,
    tokensPerMinute: 100000
  }
});

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const timeRange = searchParams.get('timeRange') || '30d';
    const workflowType = searchParams.get('workflowId');
    const status = searchParams.get('status');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // Build filters
    const filters: AnalyticsFilters = {};
    if (workflowType) filters.workflowType = workflowType;
    if (status) filters.status = status as any;
    if (startDate && endDate) {
      filters.dateRange = { start: startDate, end: endDate };
    }

    const stateStore = getStateStore();
    const analyticsEngine = new WorkflowAnalyticsEngine(stateStore, aiModelManager);

    // Generate comprehensive metrics
    const metrics = await analyticsEngine.generateMetrics(timeRange, filters);

    // Get real-time metrics
    const realTimeMetrics = {
      activeExecutions: 0, // TODO: implement active execution tracking
      currentThroughput: 0,
      systemHealth: 100,
      budgetUtilization: 0
    };

    // Get cost breakdown
    const costTracker = analyticsEngine.getCostTracker();
    const costReport = await costTracker.generateCostReport(timeRange);

    return NextResponse.json({
      success: true,
      data: {
        metrics,
        realTimeMetrics,
        costReport,
        timeRange,
        filters,
        generatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Analytics metrics error:', error);
    return NextResponse.json(
      {
        error: 'Failed to generate analytics metrics',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, data } = body;

    const stateStore = getStateStore();
    const analyticsEngine = new WorkflowAnalyticsEngine(stateStore, aiModelManager);

    switch (action) {
      case 'track_cost':
        // Track AI cost for a specific execution
        const { executionId, agentType, tokens, cost } = data;
        const costTracker = analyticsEngine.getCostTracker();
        costTracker.trackTokenUsage(agentType, tokens, cost);
        
        return NextResponse.json({
          success: true,
          message: 'Cost tracked successfully'
        });

      case 'calculate_workflow_cost':
        // Calculate total cost for a workflow execution
        const { executionId: execId } = data;
        const costBreakdown = await analyticsEngine.getCostTracker().calculateWorkflowCost(execId);
        
        return NextResponse.json({
          success: true,
          data: costBreakdown
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Analytics metrics POST error:', error);
    return NextResponse.json(
      {
        error: 'Failed to process analytics request',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
