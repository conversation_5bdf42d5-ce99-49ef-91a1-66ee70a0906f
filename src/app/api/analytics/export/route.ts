/**
 * Analytics Export API
 * GET /api/analytics/export - Export analytics data in various formats
 */

import { NextRequest, NextResponse } from 'next/server';
import { getStateStore } from '../../../../core/workflow/singleton';
import { WorkflowAnalyticsEngine, AnalyticsFilters } from '../../../../core/analytics/workflow-analytics-engine';
import { AIModelManager } from '../../../../core/ai/model-manager';

// Initialize AI Model Manager
const aiModelManager = new AIModelManager({
  defaultProvider: 'openai',
  defaultModel: 'gpt-3.5-turbo',
  providers: {
    openai: {
      apiKey: process.env.OPENAI_API_KEY || ''
    },
    anthropic: {
      apiKey: process.env.ANTHROPIC_API_KEY || ''
    }
  },
  costTracking: true,
  rateLimiting: {
    enabled: false,
    requestsPerMinute: 60,
    tokensPerMinute: 100000
  }
});

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const format = searchParams.get('format') || 'json'; // json, csv, pdf
    const timeRange = searchParams.get('timeRange') || '30d';
    const workflowType = searchParams.get('workflowId');
    const includeDetails = searchParams.get('includeDetails') === 'true';

    // Build filters
    const filters: AnalyticsFilters = {};
    if (workflowType) filters.workflowType = workflowType;

    const stateStore = getStateStore();
    const analyticsEngine = new WorkflowAnalyticsEngine(stateStore, aiModelManager);

    // Generate comprehensive analytics data
    const metrics = await analyticsEngine.generateMetrics(timeRange, filters);
    const costTracker = analyticsEngine.getCostTracker();
    const costReport = await costTracker.generateCostReport(timeRange);

    const exportData = {
      metadata: {
        exportedAt: new Date().toISOString(),
        timeRange,
        format,
        includeDetails,
        filters
      },
      summary: {
        totalExecutions: metrics.totalExecutions,
        successRate: metrics.successRate,
        averageExecutionTime: metrics.averageExecutionTime,
        totalCost: costReport.totalCost,
        averageCostPerExecution: costReport.averageCostPerExecution
      },
      metrics,
      costReport
    };

    // Handle different export formats
    switch (format.toLowerCase()) {
      case 'csv':
        return exportAsCSV(exportData);
      case 'json':
        return exportAsJSON(exportData);
      case 'pdf':
        return exportAsPDF(exportData);
      default:
        return NextResponse.json(
          { error: 'Unsupported export format. Supported formats: json, csv, pdf' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Analytics export error:', error);
    return NextResponse.json(
      {
        error: 'Failed to export analytics data',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

function exportAsJSON(data: any): NextResponse {
  const jsonData = JSON.stringify(data, null, 2);
  
  return new NextResponse(jsonData, {
    headers: {
      'Content-Type': 'application/json',
      'Content-Disposition': `attachment; filename="analytics-export-${new Date().toISOString().split('T')[0]}.json"`
    }
  });
}

function exportAsCSV(data: any): NextResponse {
  const csvRows: string[] = [];
  
  // Header
  csvRows.push('Metric,Value,Description');
  
  // Summary data
  csvRows.push(`Total Executions,${data.summary.totalExecutions},Number of workflow executions`);
  csvRows.push(`Success Rate,${data.summary.successRate.toFixed(2)}%,Percentage of successful executions`);
  csvRows.push(`Average Execution Time,${data.summary.averageExecutionTime.toFixed(0)}ms,Average time per execution`);
  csvRows.push(`Total Cost,$${data.summary.totalCost.toFixed(2)},Total AI costs`);
  csvRows.push(`Average Cost Per Execution,$${data.summary.averageCostPerExecution.toFixed(2)},Average cost per execution`);
  
  // Cost breakdown by agent
  csvRows.push(''); // Empty row
  csvRows.push('Agent Type,Cost,Percentage');
  const totalCost = data.costReport.totalCost;
  Object.entries(data.costReport.costByAgent).forEach(([agent, cost]: [string, any]) => {
    const percentage = totalCost > 0 ? ((cost / totalCost) * 100).toFixed(1) : '0';
    csvRows.push(`${agent},$${cost.toFixed(2)},${percentage}%`);
  });
  
  // Step performance (if available)
  if (data.metrics.stepPerformance && data.metrics.stepPerformance.length > 0) {
    csvRows.push(''); // Empty row
    csvRows.push('Step Name,Average Time,Success Rate,Average Cost');
    data.metrics.stepPerformance.forEach((step: any) => {
      csvRows.push(`${step.stepName},${step.averageExecutionTime.toFixed(0)}ms,${step.successRate.toFixed(1)}%,$${step.averageCost.toFixed(2)}`);
    });
  }
  
  const csvContent = csvRows.join('\n');
  
  return new NextResponse(csvContent, {
    headers: {
      'Content-Type': 'text/csv',
      'Content-Disposition': `attachment; filename="analytics-export-${new Date().toISOString().split('T')[0]}.csv"`
    }
  });
}

function exportAsPDF(data: any): NextResponse {
  // For now, return a simple text-based PDF content
  // In a real implementation, you'd use a PDF generation library like Puppeteer or jsPDF
  
  const pdfContent = generatePDFContent(data);
  
  return new NextResponse(pdfContent, {
    headers: {
      'Content-Type': 'application/pdf',
      'Content-Disposition': `attachment; filename="analytics-report-${new Date().toISOString().split('T')[0]}.pdf"`
    }
  });
}

function generatePDFContent(data: any): string {
  // This is a placeholder - in a real implementation, you'd generate actual PDF content
  const report = `
WORKFLOW ANALYTICS REPORT
Generated: ${data.metadata.exportedAt}
Time Range: ${data.metadata.timeRange}

EXECUTIVE SUMMARY
================
Total Executions: ${data.summary.totalExecutions}
Success Rate: ${data.summary.successRate.toFixed(2)}%
Average Execution Time: ${data.summary.averageExecutionTime.toFixed(0)}ms
Total AI Costs: $${data.summary.totalCost.toFixed(2)}
Average Cost Per Execution: $${data.summary.averageCostPerExecution.toFixed(2)}

COST BREAKDOWN BY AGENT
=======================
${Object.entries(data.costReport.costByAgent).map(([agent, cost]: [string, any]) => 
  `${agent}: $${cost.toFixed(2)}`
).join('\n')}

QUALITY METRICS
===============
Average Quality Score: ${data.metrics.qualityMetrics.averageQualityScore}
Improvement Rate: ${data.metrics.qualityMetrics.improvementRate}%

RECOMMENDATIONS
===============
${data.costReport.recommendations.map((rec: any) => 
  `- ${rec.description} (Potential savings: $${rec.potentialSavings?.toFixed(2) || 'N/A'})`
).join('\n')}

This report was generated by AuthencioCMS Analytics Engine.
For detailed analysis and interactive charts, please use the web dashboard.
`;

  return report;
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, data } = body;

    switch (action) {
      case 'schedule_export':
        // Schedule regular exports (placeholder)
        return NextResponse.json({
          success: true,
          message: 'Export scheduled successfully',
          scheduleId: `schedule-${Date.now()}`
        });

      case 'get_export_history':
        // Get export history (placeholder)
        return NextResponse.json({
          success: true,
          data: {
            exports: [
              {
                id: 'export-1',
                format: 'csv',
                createdAt: new Date().toISOString(),
                size: '2.5 KB',
                status: 'completed'
              }
            ]
          }
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Analytics export POST error:', error);
    return NextResponse.json(
      {
        error: 'Failed to process export request',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
