/**
 * Quality Validation API
 * POST /api/quality/validate - Validate content quality
 */

import { NextRequest, NextResponse } from 'next/server';
import { QualityValidator, QualityConfig } from '../../../../core/quality/quality-validator';
import { ReadabilityAnalyzer } from '../../../../core/quality/readability-analyzer';
import { Brand<PERSON>omplianceChecker, BrandGuidelines } from '../../../../core/quality/brand-compliance-checker';
import { QualityScorer } from '../../../../core/quality/quality-scorer';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { content, config, options } = body;

    if (!content || typeof content !== 'string') {
      return NextResponse.json(
        { error: 'Content is required and must be a string' },
        { status: 400 }
      );
    }

    if (content.length < 10) {
      return NextResponse.json(
        { error: 'Content is too short for meaningful analysis' },
        { status: 400 }
      );
    }

    // Initialize validators
    const qualityValidator = new QualityValidator();
    const readabilityAnalyzer = new ReadabilityAnalyzer();
    const qualityScorer = new QualityScorer();

    // Parse configuration
    const qualityConfig: QualityConfig = {
      seoConfig: {
        targetKeywords: config?.seoConfig?.targetKeywords || [],
        minWordCount: config?.seoConfig?.minWordCount || 300,
        maxWordCount: config?.seoConfig?.maxWordCount || 2000,
      },
      brandGuidelines: config?.brandGuidelines || {
        tone: 'professional',
        vocabulary: [],
        prohibitedWords: []
      },
      readabilityTarget: {
        gradeLevel: config?.readabilityTarget?.gradeLevel || 8,
        fleschScore: config?.readabilityTarget?.fleschScore || 70
      },
      qualityThresholds: {
        minimum: config?.qualityThresholds?.minimum || 60,
        target: config?.qualityThresholds?.target || 80,
        excellent: config?.qualityThresholds?.excellent || 90
      }
    };

    console.log('🔍 Starting quality validation for content...');
    const startTime = Date.now();

    // Run comprehensive quality validation
    const validationResult = await qualityValidator.validateContent(content, qualityConfig);

    // Run detailed readability analysis
    const readabilityAnalysis = readabilityAnalyzer.analyze(content);

    // Initialize brand compliance checker if guidelines provided
    let brandComplianceResult = null;
    if (config?.brandGuidelines) {
      const brandGuidelines: BrandGuidelines = {
        tone: {
          primary: config.brandGuidelines.tone || 'professional',
          characteristics: config.brandGuidelines.characteristics || [],
          avoidCharacteristics: config.brandGuidelines.avoidCharacteristics || []
        },
        vocabulary: {
          preferredTerms: config.brandGuidelines.preferredTerms || {},
          prohibitedWords: config.brandGuidelines.prohibitedWords || [],
          brandTerms: config.brandGuidelines.brandTerms || [],
          industryJargon: {
            allowed: config.brandGuidelines.industryJargon?.allowed ?? true,
            maxPercentage: config.brandGuidelines.industryJargon?.maxPercentage || 10
          }
        },
        style: {
          personPerspective: config.brandGuidelines.style?.personPerspective || 'mixed',
          voiceActive: config.brandGuidelines.style?.voiceActive ?? true,
          contractions: config.brandGuidelines.style?.contractions ?? true,
          sentenceLength: {
            max: config.brandGuidelines.style?.sentenceLength?.max || 25,
            preferred: config.brandGuidelines.style?.sentenceLength?.preferred || 20
          }
        },
        messaging: {
          keyMessages: config.brandGuidelines.messaging?.keyMessages || [],
          valuePropositions: config.brandGuidelines.messaging?.valuePropositions || [],
          callsToAction: config.brandGuidelines.messaging?.callsToAction || []
        },
        compliance: {
          inclusiveLanguage: config.brandGuidelines.compliance?.inclusiveLanguage ?? true,
          accessibility: config.brandGuidelines.compliance?.accessibility ?? true,
          legalRequirements: config.brandGuidelines.compliance?.legalRequirements || []
        }
      };

      const brandChecker = new BrandComplianceChecker(brandGuidelines);
      brandComplianceResult = brandChecker.check(content);
    }

    // Calculate comprehensive quality score
    const qualityScoreResult = qualityScorer.calculateOverallScore(
      {
        readability: readabilityAnalysis,
        seo: validationResult.breakdown.seo,
        brandCompliance: brandComplianceResult,
        grammar: validationResult.breakdown.grammar,
        structure: validationResult.breakdown.structure
      },
      content,
      options?.scoreWeights
    );

    const executionTime = Date.now() - startTime;

    console.log(`✅ Quality validation completed in ${executionTime}ms`);
    console.log(`📊 Overall quality score: ${qualityScoreResult.overallScore}/100`);

    // Compile comprehensive response
    const response = {
      success: true,
      data: {
        // Overall results
        overallScore: qualityScoreResult.overallScore,
        qualityGrade: qualityScoreResult.qualityGrade,
        
        // Detailed validation results
        validation: validationResult,
        readability: readabilityAnalysis,
        brandCompliance: brandComplianceResult,
        qualityScore: qualityScoreResult,
        
        // Summary metrics
        summary: {
          wordCount: content.split(/\s+/).filter(word => word.length > 0).length,
          readabilityGrade: readabilityAnalysis.metrics.readabilityGrade,
          seoScore: validationResult.breakdown.seo.score,
          grammarScore: validationResult.breakdown.grammar.score,
          structureScore: validationResult.breakdown.structure.score,
          brandComplianceScore: brandComplianceResult?.overallScore || null
        },
        
        // Recommendations
        recommendations: {
          critical: validationResult.recommendations.filter(r => r.type === 'critical'),
          important: validationResult.recommendations.filter(r => r.type === 'important'),
          suggestions: validationResult.recommendations.filter(r => r.type === 'suggestion'),
          readability: readabilityAnalysis.recommendations,
          brandCompliance: brandComplianceResult?.recommendations || []
        },
        
        // Performance metrics
        performance: {
          executionTime,
          aiCosts: validationResult.aiCosts,
          validatedAt: validationResult.validatedAt
        }
      }
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Quality validation error:', error);
    return NextResponse.json(
      {
        error: 'Failed to validate content quality',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    switch (action) {
      case 'config':
        // Return default configuration options
        return NextResponse.json({
          success: true,
          data: {
            defaultConfig: {
              seoConfig: {
                minWordCount: 300,
                maxWordCount: 2000,
                targetKeywords: []
              },
              readabilityTarget: {
                gradeLevel: 8,
                fleschScore: 70
              },
              qualityThresholds: {
                minimum: 60,
                target: 80,
                excellent: 90
              }
            },
            scoreWeights: {
              readability: 0.20,
              seo: 0.20,
              brandCompliance: 0.15,
              grammar: 0.15,
              structure: 0.10,
              engagement: 0.10,
              originality: 0.10
            },
            supportedFeatures: [
              'readability_analysis',
              'seo_validation',
              'brand_compliance',
              'grammar_checking',
              'structure_analysis',
              'engagement_scoring',
              'originality_assessment'
            ]
          }
        });

      case 'metrics':
        // Return available quality metrics
        return NextResponse.json({
          success: true,
          data: {
            readabilityMetrics: [
              'fleschReadingEase',
              'fleschKincaidGradeLevel',
              'gunningFogIndex',
              'colemanLiauIndex',
              'automatedReadabilityIndex',
              'smogIndex'
            ],
            seoMetrics: [
              'keywordDensity',
              'metaDescription',
              'headingStructure',
              'wordCount'
            ],
            brandMetrics: [
              'toneCompliance',
              'vocabularyCompliance',
              'styleCompliance',
              'messagingAlignment'
            ],
            qualityCategories: [
              'readability',
              'seo',
              'brandCompliance',
              'grammar',
              'structure',
              'engagement',
              'originality'
            ]
          }
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action parameter' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Quality validation GET error:', error);
    return NextResponse.json(
      {
        error: 'Failed to process request',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
