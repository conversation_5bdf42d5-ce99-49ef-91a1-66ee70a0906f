/**
 * Quality Analysis API
 * POST /api/quality/analyze - Analyze specific quality aspects
 */

import { NextRequest, NextResponse } from 'next/server';
import { ReadabilityAnalyzer } from '../../../../core/quality/readability-analyzer';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BrandGuidelines } from '../../../../core/quality/brand-compliance-checker';
import { QualityScorer } from '../../../../core/quality/quality-scorer';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { content, analysisType, config } = body;

    if (!content || typeof content !== 'string') {
      return NextResponse.json(
        { error: 'Content is required and must be a string' },
        { status: 400 }
      );
    }

    if (!analysisType) {
      return NextResponse.json(
        { error: 'Analysis type is required' },
        { status: 400 }
      );
    }

    const startTime = Date.now();
    let result;

    switch (analysisType) {
      case 'readability':
        result = await analyzeReadability(content, config);
        break;
      
      case 'brand_compliance':
        result = await analyzeBrandCompliance(content, config);
        break;
      
      case 'engagement':
        result = await analyzeEngagement(content, config);
        break;
      
      case 'originality':
        result = await analyzeOriginality(content, config);
        break;
      
      case 'comprehensive':
        result = await analyzeComprehensive(content, config);
        break;
      
      default:
        return NextResponse.json(
          { error: `Unsupported analysis type: ${analysisType}` },
          { status: 400 }
        );
    }

    const executionTime = Date.now() - startTime;

    return NextResponse.json({
      success: true,
      data: {
        analysisType,
        result,
        metadata: {
          contentLength: content.length,
          wordCount: content.split(/\s+/).filter(word => word.length > 0).length,
          executionTime,
          analyzedAt: new Date().toISOString()
        }
      }
    });

  } catch (error) {
    console.error('Quality analysis error:', error);
    return NextResponse.json(
      {
        error: 'Failed to analyze content',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

async function analyzeReadability(content: string, config: any) {
  const analyzer = new ReadabilityAnalyzer();
  const analysis = analyzer.analyze(content);
  
  // Add target recommendations if specified
  if (config?.audience) {
    const targetRecommendations = analyzer.getTargetRecommendations(config.audience);
    return {
      ...analysis,
      targetRecommendations
    };
  }
  
  return analysis;
}

async function analyzeBrandCompliance(content: string, config: any) {
  if (!config?.brandGuidelines) {
    throw new Error('Brand guidelines configuration is required for brand compliance analysis');
  }

  const brandGuidelines: BrandGuidelines = {
    tone: {
      primary: config.brandGuidelines.tone || 'professional',
      characteristics: config.brandGuidelines.characteristics || [],
      avoidCharacteristics: config.brandGuidelines.avoidCharacteristics || []
    },
    vocabulary: {
      preferredTerms: config.brandGuidelines.preferredTerms || {},
      prohibitedWords: config.brandGuidelines.prohibitedWords || [],
      brandTerms: config.brandGuidelines.brandTerms || [],
      industryJargon: {
        allowed: config.brandGuidelines.industryJargon?.allowed ?? true,
        maxPercentage: config.brandGuidelines.industryJargon?.maxPercentage || 10
      }
    },
    style: {
      personPerspective: config.brandGuidelines.style?.personPerspective || 'mixed',
      voiceActive: config.brandGuidelines.style?.voiceActive ?? true,
      contractions: config.brandGuidelines.style?.contractions ?? true,
      sentenceLength: {
        max: config.brandGuidelines.style?.sentenceLength?.max || 25,
        preferred: config.brandGuidelines.style?.sentenceLength?.preferred || 20
      }
    },
    messaging: {
      keyMessages: config.brandGuidelines.messaging?.keyMessages || [],
      valuePropositions: config.brandGuidelines.messaging?.valuePropositions || [],
      callsToAction: config.brandGuidelines.messaging?.callsToAction || []
    },
    compliance: {
      inclusiveLanguage: config.brandGuidelines.compliance?.inclusiveLanguage ?? true,
      accessibility: config.brandGuidelines.compliance?.accessibility ?? true,
      legalRequirements: config.brandGuidelines.compliance?.legalRequirements || []
    }
  };

  const checker = new BrandComplianceChecker(brandGuidelines);
  return checker.check(content);
}

async function analyzeEngagement(content: string, config: any) {
  const scorer = new QualityScorer();
  
  // Calculate engagement score using the private method through comprehensive scoring
  const mockAssessments = {
    readability: { score: 85 },
    seo: { score: 80 },
    grammar: { score: 90 },
    structure: { score: 85 }
  };
  
  const result = scorer.calculateOverallScore(mockAssessments, content, {
    engagement: 1.0, // Focus entirely on engagement
    readability: 0,
    seo: 0,
    brandCompliance: 0,
    grammar: 0,
    structure: 0,
    originality: 0
  });

  return {
    engagementScore: result.categoryScores.engagement,
    overallScore: result.overallScore,
    strengths: result.strengths,
    weaknesses: result.weaknesses,
    recommendations: generateEngagementRecommendations(content),
    factors: analyzeEngagementFactors(content)
  };
}

async function analyzeOriginality(content: string, config: any) {
  const scorer = new QualityScorer();
  
  // Calculate originality score using the private method through comprehensive scoring
  const mockAssessments = {
    readability: { score: 85 },
    seo: { score: 80 },
    grammar: { score: 90 },
    structure: { score: 85 }
  };
  
  const result = scorer.calculateOverallScore(mockAssessments, content, {
    originality: 1.0, // Focus entirely on originality
    readability: 0,
    seo: 0,
    brandCompliance: 0,
    grammar: 0,
    structure: 0,
    engagement: 0
  });

  return {
    originalityScore: result.categoryScores.originality,
    overallScore: result.overallScore,
    strengths: result.strengths,
    weaknesses: result.weaknesses,
    recommendations: generateOriginalityRecommendations(content),
    factors: analyzeOriginalityFactors(content)
  };
}

async function analyzeComprehensive(content: string, config: any) {
  const readabilityAnalyzer = new ReadabilityAnalyzer();
  const qualityScorer = new QualityScorer();
  
  // Run readability analysis
  const readabilityAnalysis = readabilityAnalyzer.analyze(content);
  
  // Run brand compliance if configured
  let brandComplianceResult = null;
  if (config?.brandGuidelines) {
    brandComplianceResult = await analyzeBrandCompliance(content, config);
  }
  
  // Calculate comprehensive quality score
  const qualityScoreResult = qualityScorer.calculateOverallScore(
    {
      readability: readabilityAnalysis,
      brandCompliance: brandComplianceResult,
      grammar: { score: 85 }, // Mock grammar score
      structure: { score: 80 }, // Mock structure score
      seo: { score: 75 } // Mock SEO score
    },
    content,
    config?.scoreWeights
  );

  return {
    readability: readabilityAnalysis,
    brandCompliance: brandComplianceResult,
    qualityScore: qualityScoreResult,
    summary: {
      overallScore: qualityScoreResult.overallScore,
      qualityGrade: qualityScoreResult.qualityGrade,
      strengths: qualityScoreResult.strengths,
      weaknesses: qualityScoreResult.weaknesses,
      improvementPotential: qualityScoreResult.improvementPotential
    }
  };
}

// Helper functions for engagement analysis
function generateEngagementRecommendations(content: string): string[] {
  const recommendations: string[] = [];
  
  // Check for hook strength
  const firstSentence = content.split(/[.!?]/)[0];
  if (!firstSentence.includes('?') && !firstSentence.match(/\d+%/)) {
    recommendations.push('Start with a compelling hook (question, statistic, or surprising statement)');
  }
  
  // Check for call-to-action
  const ctaWords = ['click', 'download', 'subscribe', 'sign up', 'get started', 'learn more'];
  const hasCTA = ctaWords.some(word => content.toLowerCase().includes(word));
  if (!hasCTA) {
    recommendations.push('Include clear call-to-action statements');
  }
  
  // Check for personal connection
  const youCount = (content.match(/\byou\b/gi) || []).length;
  if (youCount < 3) {
    recommendations.push('Use more direct address to connect with readers');
  }

  return recommendations;
}

function analyzeEngagementFactors(content: string) {
  const firstSentence = content.split(/[.!?]/)[0];
  const hasQuestion = firstSentence.includes('?');
  const hasStatistic = firstSentence.match(/\d+%|\d+\s*(percent|million|billion)/);
  const youCount = (content.match(/\byou\b/gi) || []).length;
  const questionCount = (content.match(/\?/g) || []).length;
  
  return {
    hookStrength: hasQuestion || hasStatistic ? 80 : 50,
    personalConnection: Math.min(youCount * 10, 100),
    interactivity: Math.min(questionCount * 15, 100),
    emotionalAppeal: analyzeEmotionalWords(content)
  };
}

function generateOriginalityRecommendations(content: string): string[] {
  const recommendations: string[] = [];
  
  // Check for data and insights
  const hasData = content.match(/\d+%|\d+\s*(study|research|survey)/gi);
  if (!hasData) {
    recommendations.push('Include data, statistics, or research findings to add credibility');
  }
  
  // Check for unique perspective
  const contrarianWords = ['however', 'contrary', 'unlike', 'different'];
  const hasContrarian = contrarianWords.some(word => content.toLowerCase().includes(word));
  if (!hasContrarian) {
    recommendations.push('Offer a unique perspective or contrarian viewpoint');
  }
  
  // Check for creative elements
  const creativeWords = ['imagine', 'picture', 'like', 'as if'];
  const hasCreative = creativeWords.some(word => content.toLowerCase().includes(word));
  if (!hasCreative) {
    recommendations.push('Add creative elements like metaphors or analogies');
  }

  return recommendations;
}

function analyzeOriginalityFactors(content: string) {
  const hasData = content.match(/\d+%|\d+\s*(study|research|survey)/gi);
  const contrarianWords = ['however', 'contrary', 'unlike', 'different'];
  const hasContrarian = contrarianWords.some(word => content.toLowerCase().includes(word));
  const creativeWords = ['imagine', 'picture', 'like', 'as if'];
  const hasCreative = creativeWords.some(word => content.toLowerCase().includes(word));
  
  return {
    uniqueInsights: hasData ? 80 : 40,
    freshPerspective: hasContrarian ? 75 : 45,
    creativeElements: hasCreative ? 70 : 50,
    valueAddition: analyzeValueWords(content)
  };
}

function analyzeEmotionalWords(content: string): number {
  const emotionalWords = ['amazing', 'incredible', 'fantastic', 'excellent', 'outstanding', 'remarkable'];
  const matches = emotionalWords.filter(word => content.toLowerCase().includes(word)).length;
  return Math.min(50 + (matches * 10), 100);
}

function analyzeValueWords(content: string): number {
  const valueWords = ['benefit', 'advantage', 'value', 'save', 'improve', 'increase'];
  const matches = valueWords.filter(word => content.toLowerCase().includes(word)).length;
  return Math.min(50 + (matches * 8), 100);
}
