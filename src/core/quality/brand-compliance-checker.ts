/**
 * Brand Compliance Checker
 * 
 * Validates content against brand guidelines and style requirements
 */

export interface BrandGuidelines {
  tone: {
    primary: 'professional' | 'casual' | 'friendly' | 'authoritative' | 'conversational' | 'formal';
    characteristics: string[];
    avoidCharacteristics: string[];
  };
  vocabulary: {
    preferredTerms: Record<string, string[]>; // category -> preferred terms
    prohibitedWords: string[];
    brandTerms: string[]; // Must be used consistently
    industryJargon: {
      allowed: boolean;
      maxPercentage?: number;
    };
  };
  style: {
    personPerspective: 'first' | 'second' | 'third' | 'mixed';
    voiceActive: boolean; // Prefer active voice
    contractions: boolean; // Allow contractions
    sentenceLength: {
      max: number;
      preferred: number;
    };
  };
  messaging: {
    keyMessages: string[];
    valuePropositions: string[];
    callsToAction: string[];
  };
  compliance: {
    inclusiveLanguage: boolean;
    accessibility: boolean;
    legalRequirements: string[];
  };
}

export interface BrandComplianceResult {
  overallScore: number;
  breakdown: {
    toneCompliance: ToneComplianceResult;
    vocabularyCompliance: VocabularyComplianceResult;
    styleCompliance: StyleComplianceResult;
    messagingAlignment: MessagingAlignmentResult;
    complianceChecks: ComplianceCheckResult;
  };
  violations: BrandViolation[];
  recommendations: BrandRecommendation[];
  analysisDetails: {
    wordCount: number;
    sentenceCount: number;
    keywordMatches: Record<string, number>;
    toneIndicators: Record<string, number>;
  };
}

export interface ToneComplianceResult {
  score: number;
  detectedTone: string;
  confidence: number;
  toneIndicators: Array<{
    indicator: string;
    weight: number;
    examples: string[];
  }>;
  recommendations: string[];
}

export interface VocabularyComplianceResult {
  score: number;
  preferredTermsUsage: number;
  prohibitedWordsFound: string[];
  brandTermsConsistency: number;
  jargonPercentage: number;
  recommendations: string[];
}

export interface StyleComplianceResult {
  score: number;
  perspectiveConsistency: number;
  activeVoicePercentage: number;
  contractionUsage: number;
  sentenceLengthCompliance: number;
  recommendations: string[];
}

export interface MessagingAlignmentResult {
  score: number;
  keyMessageAlignment: number;
  valuePropositionPresence: number;
  callToActionEffectiveness: number;
  recommendations: string[];
}

export interface ComplianceCheckResult {
  score: number;
  inclusiveLanguageScore: number;
  accessibilityScore: number;
  legalComplianceScore: number;
  violations: string[];
  recommendations: string[];
}

export interface BrandViolation {
  type: 'tone' | 'vocabulary' | 'style' | 'messaging' | 'compliance';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  location: {
    start: number;
    end: number;
    context: string;
  };
  suggestion: string;
}

export interface BrandRecommendation {
  category: 'tone' | 'vocabulary' | 'style' | 'messaging' | 'compliance';
  priority: 'low' | 'medium' | 'high';
  title: string;
  description: string;
  action: string;
  impact: string;
}

export class BrandComplianceChecker {
  private guidelines: BrandGuidelines;

  constructor(guidelines: BrandGuidelines) {
    this.guidelines = guidelines;
  }

  /**
   * Check content against brand guidelines
   */
  check(content: string): BrandComplianceResult {
    const analysisDetails = this.analyzeContent(content);
    
    const breakdown = {
      toneCompliance: this.checkToneCompliance(content, analysisDetails),
      vocabularyCompliance: this.checkVocabularyCompliance(content, analysisDetails),
      styleCompliance: this.checkStyleCompliance(content, analysisDetails),
      messagingAlignment: this.checkMessagingAlignment(content, analysisDetails),
      complianceChecks: this.checkCompliance(content, analysisDetails)
    };

    const violations = this.collectViolations(breakdown);
    const recommendations = this.generateRecommendations(breakdown);
    const overallScore = this.calculateOverallScore(breakdown);

    return {
      overallScore,
      breakdown,
      violations,
      recommendations,
      analysisDetails
    };
  }

  /**
   * Analyze content for basic metrics
   */
  private analyzeContent(content: string) {
    const words = content.toLowerCase().split(/\s+/).filter(word => word.length > 0);
    const sentences = content.split(/[.!?]+/).filter(sentence => sentence.trim().length > 0);
    
    // Extract keywords and tone indicators
    const keywordMatches: Record<string, number> = {};
    const toneIndicators: Record<string, number> = {};

    // Count brand terms
    this.guidelines.vocabulary.brandTerms.forEach(term => {
      const regex = new RegExp(`\\b${term.toLowerCase()}\\b`, 'gi');
      const matches = content.match(regex);
      keywordMatches[term] = matches ? matches.length : 0;
    });

    // Analyze tone indicators
    this.analyzeToneIndicators(content, toneIndicators);

    return {
      wordCount: words.length,
      sentenceCount: sentences.length,
      keywordMatches,
      toneIndicators,
      words,
      sentences
    };
  }

  /**
   * Check tone compliance
   */
  private checkToneCompliance(content: string, analysis: any): ToneComplianceResult {
    const targetTone = this.guidelines.tone.primary;
    const characteristics = this.guidelines.tone.characteristics;
    const avoidCharacteristics = this.guidelines.tone.avoidCharacteristics;

    let score = 100;
    const detectedTone = this.detectTone(content);
    const confidence = this.calculateToneConfidence(content, targetTone);
    const toneIndicators = this.getToneIndicators(content);
    const recommendations: string[] = [];

    // Check for avoided characteristics
    avoidCharacteristics.forEach(characteristic => {
      if (this.hasCharacteristic(content, characteristic)) {
        score -= 15;
        recommendations.push(`Avoid ${characteristic} tone - detected in content`);
      }
    });

    // Check for required characteristics
    const presentCharacteristics = characteristics.filter(char => 
      this.hasCharacteristic(content, char)
    );
    
    if (presentCharacteristics.length < characteristics.length * 0.6) {
      score -= 20;
      recommendations.push(`Strengthen ${targetTone} tone by incorporating more characteristic language`);
    }

    return {
      score: Math.max(score, 0),
      detectedTone,
      confidence,
      toneIndicators,
      recommendations
    };
  }

  /**
   * Check vocabulary compliance
   */
  private checkVocabularyCompliance(content: string, analysis: any): VocabularyComplianceResult {
    let score = 100;
    const recommendations: string[] = [];
    const prohibitedWordsFound: string[] = [];

    // Check for prohibited words
    this.guidelines.vocabulary.prohibitedWords.forEach(word => {
      if (analysis.words.includes(word.toLowerCase())) {
        prohibitedWordsFound.push(word);
        score -= 10;
      }
    });

    // Check preferred terms usage
    const preferredTermsUsage = this.calculatePreferredTermsUsage(content);
    if (preferredTermsUsage < 70) {
      score -= 15;
      recommendations.push('Use more brand-preferred terminology');
    }

    // Check brand terms consistency
    const brandTermsConsistency = this.calculateBrandTermsConsistency(content);
    if (brandTermsConsistency < 90) {
      score -= 10;
      recommendations.push('Ensure consistent use of brand terms');
    }

    // Check jargon percentage
    const jargonPercentage = this.calculateJargonPercentage(content);
    const maxJargon = this.guidelines.vocabulary.industryJargon.maxPercentage || 10;
    if (jargonPercentage > maxJargon) {
      score -= 10;
      recommendations.push(`Reduce industry jargon (${jargonPercentage}% > ${maxJargon}%)`);
    }

    if (prohibitedWordsFound.length > 0) {
      recommendations.push(`Remove prohibited words: ${prohibitedWordsFound.join(', ')}`);
    }

    return {
      score: Math.max(score, 0),
      preferredTermsUsage,
      prohibitedWordsFound,
      brandTermsConsistency,
      jargonPercentage,
      recommendations
    };
  }

  /**
   * Check style compliance
   */
  private checkStyleCompliance(content: string, analysis: any): StyleComplianceResult {
    let score = 100;
    const recommendations: string[] = [];

    // Check perspective consistency
    const perspectiveConsistency = this.checkPerspectiveConsistency(content);
    if (perspectiveConsistency < 80) {
      score -= 15;
      recommendations.push(`Maintain consistent ${this.guidelines.style.personPerspective} person perspective`);
    }

    // Check active voice usage
    const activeVoicePercentage = this.calculateActiveVoicePercentage(content);
    if (this.guidelines.style.voiceActive && activeVoicePercentage < 70) {
      score -= 10;
      recommendations.push('Use more active voice constructions');
    }

    // Check contraction usage
    const contractionUsage = this.calculateContractionUsage(content);
    if (!this.guidelines.style.contractions && contractionUsage > 5) {
      score -= 5;
      recommendations.push('Avoid contractions per brand guidelines');
    }

    // Check sentence length compliance
    const sentenceLengthCompliance = this.checkSentenceLengthCompliance(content);
    if (sentenceLengthCompliance < 80) {
      score -= 10;
      recommendations.push(`Keep sentences under ${this.guidelines.style.sentenceLength.max} words`);
    }

    return {
      score: Math.max(score, 0),
      perspectiveConsistency,
      activeVoicePercentage,
      contractionUsage,
      sentenceLengthCompliance,
      recommendations
    };
  }

  /**
   * Check messaging alignment
   */
  private checkMessagingAlignment(content: string, analysis: any): MessagingAlignmentResult {
    let score = 100;
    const recommendations: string[] = [];

    // Check key message alignment
    const keyMessageAlignment = this.calculateKeyMessageAlignment(content);
    if (keyMessageAlignment < 60) {
      score -= 20;
      recommendations.push('Better align content with key brand messages');
    }

    // Check value proposition presence
    const valuePropositionPresence = this.calculateValuePropositionPresence(content);
    if (valuePropositionPresence < 40) {
      score -= 15;
      recommendations.push('Include more value proposition elements');
    }

    // Check call-to-action effectiveness
    const callToActionEffectiveness = this.calculateCallToActionEffectiveness(content);
    if (callToActionEffectiveness < 50) {
      score -= 10;
      recommendations.push('Strengthen call-to-action language');
    }

    return {
      score: Math.max(score, 0),
      keyMessageAlignment,
      valuePropositionPresence,
      callToActionEffectiveness,
      recommendations
    };
  }

  /**
   * Check compliance requirements
   */
  private checkCompliance(content: string, analysis: any): ComplianceCheckResult {
    let score = 100;
    const violations: string[] = [];
    const recommendations: string[] = [];

    // Check inclusive language
    const inclusiveLanguageScore = this.checkInclusiveLanguage(content);
    if (inclusiveLanguageScore < 90) {
      score -= 15;
      recommendations.push('Review content for inclusive language');
    }

    // Check accessibility
    const accessibilityScore = this.checkAccessibility(content);
    if (accessibilityScore < 80) {
      score -= 10;
      recommendations.push('Improve content accessibility');
    }

    // Check legal compliance
    const legalComplianceScore = this.checkLegalCompliance(content);
    if (legalComplianceScore < 100) {
      score -= 20;
      violations.push('Legal compliance issues detected');
    }

    return {
      score: Math.max(score, 0),
      inclusiveLanguageScore,
      accessibilityScore,
      legalComplianceScore,
      violations,
      recommendations
    };
  }

  // Helper methods (simplified implementations)
  private analyzeToneIndicators(content: string, indicators: Record<string, number>): void {
    const toneWords = {
      professional: ['expertise', 'solution', 'strategy', 'analysis'],
      casual: ['easy', 'simple', 'fun', 'cool'],
      friendly: ['welcome', 'help', 'support', 'together'],
      authoritative: ['proven', 'established', 'leading', 'expert']
    };

    Object.entries(toneWords).forEach(([tone, words]) => {
      indicators[tone] = words.filter(word => 
        content.toLowerCase().includes(word)
      ).length;
    });
  }

  private detectTone(content: string): string {
    // Simplified tone detection
    return this.guidelines.tone.primary;
  }

  private calculateToneConfidence(content: string, targetTone: string): number {
    // Simplified confidence calculation
    return 85;
  }

  private getToneIndicators(content: string): Array<{indicator: string; weight: number; examples: string[]}> {
    return [
      { indicator: 'professional', weight: 0.8, examples: ['expertise', 'solution'] }
    ];
  }

  private hasCharacteristic(content: string, characteristic: string): boolean {
    // Simplified characteristic detection
    return content.toLowerCase().includes(characteristic);
  }

  private calculatePreferredTermsUsage(content: string): number {
    return 75; // Simplified calculation
  }

  private calculateBrandTermsConsistency(content: string): number {
    return 90; // Simplified calculation
  }

  private calculateJargonPercentage(content: string): number {
    return 5; // Simplified calculation
  }

  private checkPerspectiveConsistency(content: string): number {
    return 85; // Simplified calculation
  }

  private calculateActiveVoicePercentage(content: string): number {
    return 75; // Simplified calculation
  }

  private calculateContractionUsage(content: string): number {
    const contractions = content.match(/\b\w+'\w+\b/g);
    return contractions ? contractions.length : 0;
  }

  private checkSentenceLengthCompliance(content: string): number {
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const maxLength = this.guidelines.style.sentenceLength.max;
    const compliantSentences = sentences.filter(sentence => 
      sentence.split(/\s+/).length <= maxLength
    );
    return (compliantSentences.length / sentences.length) * 100;
  }

  private calculateKeyMessageAlignment(content: string): number {
    return 70; // Simplified calculation
  }

  private calculateValuePropositionPresence(content: string): number {
    return 60; // Simplified calculation
  }

  private calculateCallToActionEffectiveness(content: string): number {
    return 55; // Simplified calculation
  }

  private checkInclusiveLanguage(content: string): number {
    return 95; // Simplified calculation
  }

  private checkAccessibility(content: string): number {
    return 85; // Simplified calculation
  }

  private checkLegalCompliance(content: string): number {
    return 100; // Simplified calculation
  }

  private collectViolations(breakdown: any): BrandViolation[] {
    const violations: BrandViolation[] = [];
    
    // Collect violations from each category
    if (breakdown.vocabularyCompliance.prohibitedWordsFound.length > 0) {
      breakdown.vocabularyCompliance.prohibitedWordsFound.forEach((word: string) => {
        violations.push({
          type: 'vocabulary',
          severity: 'high',
          description: `Prohibited word "${word}" found`,
          location: { start: 0, end: 0, context: word },
          suggestion: `Remove or replace "${word}" with approved alternative`
        });
      });
    }

    return violations;
  }

  private generateRecommendations(breakdown: any): BrandRecommendation[] {
    const recommendations: BrandRecommendation[] = [];
    
    // Generate recommendations based on scores
    Object.entries(breakdown).forEach(([category, result]: [string, any]) => {
      if (result.score < 80 && result.recommendations) {
        result.recommendations.forEach((rec: string) => {
          recommendations.push({
            category: category as any,
            priority: result.score < 60 ? 'high' : 'medium',
            title: `Improve ${category}`,
            description: rec,
            action: rec,
            impact: result.score < 60 ? 'High impact on brand compliance' : 'Medium impact on brand compliance'
          });
        });
      }
    });

    return recommendations;
  }

  private calculateOverallScore(breakdown: any): number {
    const weights = {
      toneCompliance: 0.25,
      vocabularyCompliance: 0.25,
      styleCompliance: 0.20,
      messagingAlignment: 0.15,
      complianceChecks: 0.15
    };

    return Math.round(
      Object.entries(breakdown).reduce((total, [key, result]: [string, any]) => {
        const weight = weights[key as keyof typeof weights] || 0;
        return total + (result.score * weight);
      }, 0)
    );
  }
}
