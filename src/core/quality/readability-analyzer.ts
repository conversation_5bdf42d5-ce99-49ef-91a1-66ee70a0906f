/**
 * Readability Analyzer
 * 
 * Advanced readability analysis using multiple metrics
 */

export interface ReadabilityMetrics {
  fleschReadingEase: number;
  fleschKincaidGradeLevel: number;
  gunningFogIndex: number;
  colemanLiauIndex: number;
  automatedReadabilityIndex: number;
  smogIndex: number;
  averageGradeLevel: number;
  readabilityGrade: 'Very Easy' | 'Easy' | 'Fairly Easy' | 'Standard' | 'Fairly Difficult' | 'Difficult' | 'Very Difficult';
}

export interface ReadabilityAnalysis {
  metrics: ReadabilityMetrics;
  textStatistics: {
    wordCount: number;
    sentenceCount: number;
    paragraphCount: number;
    characterCount: number;
    averageWordsPerSentence: number;
    averageSentencesPerParagraph: number;
    averageSyllablesPerWord: number;
    complexWordCount: number;
    complexWordPercentage: number;
  };
  recommendations: ReadabilityRecommendation[];
  score: number; // 0-100
}

export interface ReadabilityRecommendation {
  type: 'sentence_length' | 'word_complexity' | 'paragraph_structure' | 'vocabulary';
  severity: 'low' | 'medium' | 'high';
  message: string;
  suggestion: string;
  impact: string;
}

export class ReadabilityAnalyzer {
  /**
   * Analyze text readability using multiple metrics
   */
  analyze(text: string): ReadabilityAnalysis {
    const stats = this.calculateTextStatistics(text);
    const metrics = this.calculateReadabilityMetrics(stats);
    const recommendations = this.generateRecommendations(stats, metrics);
    const score = this.calculateOverallScore(metrics);

    return {
      metrics,
      textStatistics: stats,
      recommendations,
      score
    };
  }

  /**
   * Calculate basic text statistics
   */
  private calculateTextStatistics(text: string) {
    const cleanText = text.replace(/\s+/g, ' ').trim();
    
    // Count words
    const words = cleanText.split(/\s+/).filter(word => word.length > 0);
    const wordCount = words.length;
    
    // Count sentences
    const sentences = cleanText.split(/[.!?]+/).filter(sentence => sentence.trim().length > 0);
    const sentenceCount = sentences.length;
    
    // Count paragraphs
    const paragraphs = text.split(/\n\s*\n/).filter(paragraph => paragraph.trim().length > 0);
    const paragraphCount = paragraphs.length;
    
    // Count characters
    const characterCount = cleanText.length;
    
    // Calculate syllables
    const totalSyllables = words.reduce((total, word) => total + this.countSyllables(word), 0);
    
    // Count complex words (3+ syllables)
    const complexWords = words.filter(word => this.countSyllables(word) >= 3);
    const complexWordCount = complexWords.length;
    
    return {
      wordCount,
      sentenceCount,
      paragraphCount,
      characterCount,
      averageWordsPerSentence: wordCount / sentenceCount,
      averageSentencesPerParagraph: sentenceCount / paragraphCount,
      averageSyllablesPerWord: totalSyllables / wordCount,
      complexWordCount,
      complexWordPercentage: (complexWordCount / wordCount) * 100,
      totalSyllables
    };
  }

  /**
   * Calculate various readability metrics
   */
  private calculateReadabilityMetrics(stats: any): ReadabilityMetrics {
    const {
      wordCount,
      sentenceCount,
      averageWordsPerSentence,
      averageSyllablesPerWord,
      complexWordCount,
      totalSyllables
    } = stats;

    // Flesch Reading Ease
    const fleschReadingEase = 206.835 - (1.015 * averageWordsPerSentence) - (84.6 * averageSyllablesPerWord);

    // Flesch-Kincaid Grade Level
    const fleschKincaidGradeLevel = (0.39 * averageWordsPerSentence) + (11.8 * averageSyllablesPerWord) - 15.59;

    // Gunning Fog Index
    const gunningFogIndex = 0.4 * (averageWordsPerSentence + (100 * (complexWordCount / wordCount)));

    // Coleman-Liau Index
    const averageLettersPerWord = stats.characterCount / wordCount;
    const averageSentencesPer100Words = (sentenceCount / wordCount) * 100;
    const colemanLiauIndex = (0.0588 * (averageLettersPerWord * 100 / wordCount)) - (0.296 * averageSentencesPer100Words) - 15.8;

    // Automated Readability Index
    const automatedReadabilityIndex = (4.71 * (stats.characterCount / wordCount)) + (0.5 * averageWordsPerSentence) - 21.43;

    // SMOG Index (simplified)
    const smogIndex = 1.0430 * Math.sqrt(complexWordCount * (30 / sentenceCount)) + 3.1291;

    // Average grade level
    const averageGradeLevel = (fleschKincaidGradeLevel + gunningFogIndex + colemanLiauIndex + automatedReadabilityIndex + smogIndex) / 5;

    // Readability grade
    const readabilityGrade = this.getReadabilityGrade(fleschReadingEase);

    return {
      fleschReadingEase: Math.round(fleschReadingEase * 10) / 10,
      fleschKincaidGradeLevel: Math.round(fleschKincaidGradeLevel * 10) / 10,
      gunningFogIndex: Math.round(gunningFogIndex * 10) / 10,
      colemanLiauIndex: Math.round(colemanLiauIndex * 10) / 10,
      automatedReadabilityIndex: Math.round(automatedReadabilityIndex * 10) / 10,
      smogIndex: Math.round(smogIndex * 10) / 10,
      averageGradeLevel: Math.round(averageGradeLevel * 10) / 10,
      readabilityGrade
    };
  }

  /**
   * Generate readability recommendations
   */
  private generateRecommendations(stats: any, metrics: ReadabilityMetrics): ReadabilityRecommendation[] {
    const recommendations: ReadabilityRecommendation[] = [];

    // Sentence length recommendations
    if (stats.averageWordsPerSentence > 20) {
      recommendations.push({
        type: 'sentence_length',
        severity: 'high',
        message: 'Sentences are too long on average',
        suggestion: 'Break down complex sentences into shorter, clearer ones. Aim for 15-20 words per sentence.',
        impact: 'Shorter sentences improve comprehension and readability'
      });
    } else if (stats.averageWordsPerSentence > 15) {
      recommendations.push({
        type: 'sentence_length',
        severity: 'medium',
        message: 'Sentences could be shorter for better readability',
        suggestion: 'Consider breaking some longer sentences into two shorter ones.',
        impact: 'Moderate improvement in reading ease'
      });
    }

    // Word complexity recommendations
    if (stats.complexWordPercentage > 15) {
      recommendations.push({
        type: 'word_complexity',
        severity: 'high',
        message: 'High percentage of complex words',
        suggestion: 'Replace complex words with simpler alternatives where possible. Use shorter, more common words.',
        impact: 'Significantly improves accessibility for broader audience'
      });
    } else if (stats.complexWordPercentage > 10) {
      recommendations.push({
        type: 'word_complexity',
        severity: 'medium',
        message: 'Moderate use of complex words',
        suggestion: 'Consider simplifying some complex words to improve readability.',
        impact: 'Moderate improvement in reading ease'
      });
    }

    // Paragraph structure recommendations
    if (stats.averageSentencesPerParagraph > 6) {
      recommendations.push({
        type: 'paragraph_structure',
        severity: 'medium',
        message: 'Paragraphs are quite long',
        suggestion: 'Break long paragraphs into shorter ones. Aim for 3-5 sentences per paragraph.',
        impact: 'Improves visual appeal and reading flow'
      });
    }

    // Grade level recommendations
    if (metrics.averageGradeLevel > 12) {
      recommendations.push({
        type: 'vocabulary',
        severity: 'high',
        message: 'Content requires college-level reading skills',
        suggestion: 'Simplify language to reach a broader audience. Aim for 8th-10th grade level.',
        impact: 'Makes content accessible to more readers'
      });
    } else if (metrics.averageGradeLevel > 10) {
      recommendations.push({
        type: 'vocabulary',
        severity: 'medium',
        message: 'Content requires high school level reading skills',
        suggestion: 'Consider simplifying some language for broader accessibility.',
        impact: 'Moderate improvement in accessibility'
      });
    }

    // Flesch Reading Ease recommendations
    if (metrics.fleschReadingEase < 30) {
      recommendations.push({
        type: 'vocabulary',
        severity: 'high',
        message: 'Text is very difficult to read',
        suggestion: 'Significantly simplify language, use shorter sentences, and choose common words.',
        impact: 'Major improvement in readability'
      });
    } else if (metrics.fleschReadingEase < 50) {
      recommendations.push({
        type: 'vocabulary',
        severity: 'medium',
        message: 'Text is fairly difficult to read',
        suggestion: 'Simplify language and sentence structure for better readability.',
        impact: 'Noticeable improvement in reading ease'
      });
    }

    return recommendations;
  }

  /**
   * Calculate overall readability score (0-100)
   */
  private calculateOverallScore(metrics: ReadabilityMetrics): number {
    let score = 100;

    // Penalize based on grade level
    if (metrics.averageGradeLevel > 12) {
      score -= 30;
    } else if (metrics.averageGradeLevel > 10) {
      score -= 20;
    } else if (metrics.averageGradeLevel > 8) {
      score -= 10;
    }

    // Penalize based on Flesch Reading Ease
    if (metrics.fleschReadingEase < 30) {
      score -= 25;
    } else if (metrics.fleschReadingEase < 50) {
      score -= 15;
    } else if (metrics.fleschReadingEase < 60) {
      score -= 10;
    }

    // Bonus for optimal readability
    if (metrics.fleschReadingEase >= 70 && metrics.averageGradeLevel <= 9) {
      score += 5;
    }

    return Math.max(Math.min(score, 100), 0);
  }

  /**
   * Get readability grade based on Flesch Reading Ease score
   */
  private getReadabilityGrade(fleschScore: number): ReadabilityMetrics['readabilityGrade'] {
    if (fleschScore >= 90) return 'Very Easy';
    if (fleschScore >= 80) return 'Easy';
    if (fleschScore >= 70) return 'Fairly Easy';
    if (fleschScore >= 60) return 'Standard';
    if (fleschScore >= 50) return 'Fairly Difficult';
    if (fleschScore >= 30) return 'Difficult';
    return 'Very Difficult';
  }

  /**
   * Count syllables in a word
   */
  private countSyllables(word: string): number {
    word = word.toLowerCase();
    
    // Handle edge cases
    if (word.length <= 3) return 1;
    
    // Remove common endings that don't add syllables
    word = word.replace(/(?:[^laeiouy]es|ed|[^laeiouy]e)$/, '');
    word = word.replace(/^y/, '');
    
    // Count vowel groups
    const matches = word.match(/[aeiouy]{1,2}/g);
    let syllableCount = matches ? matches.length : 1;
    
    // Ensure minimum of 1 syllable
    return Math.max(syllableCount, 1);
  }

  /**
   * Get target readability recommendations based on audience
   */
  getTargetRecommendations(audience: 'general' | 'academic' | 'technical' | 'children'): {
    targetGradeLevel: number;
    targetFleschScore: number;
    description: string;
  } {
    const targets = {
      general: {
        targetGradeLevel: 8,
        targetFleschScore: 70,
        description: 'Easy to read for general audience'
      },
      academic: {
        targetGradeLevel: 12,
        targetFleschScore: 50,
        description: 'Appropriate for academic/professional content'
      },
      technical: {
        targetGradeLevel: 14,
        targetFleschScore: 40,
        description: 'Suitable for technical documentation'
      },
      children: {
        targetGradeLevel: 6,
        targetFleschScore: 80,
        description: 'Easy to read for young readers'
      }
    };

    return targets[audience];
  }
}
