/**
 * Quality Validator
 * 
 * Main validation engine for content quality assessment
 */

import { AIAgentCostTracker } from '../analytics/workflow-analytics-engine';
import { SEOAnalyzer, SEOAnalysisResult } from '../seo/analyzer';

export interface QualityConfig {
  seoConfig?: {
    targetKeywords?: string[];
    minWordCount?: number;
    maxWordCount?: number;
  };
  brandGuidelines?: {
    tone?: 'professional' | 'casual' | 'friendly' | 'authoritative';
    vocabulary?: string[];
    prohibitedWords?: string[];
  };
  readabilityTarget?: {
    gradeLevel?: number;
    fleschScore?: number;
  };
  qualityThresholds?: {
    minimum?: number;
    target?: number;
    excellent?: number;
  };
}

export interface QualityValidationResult {
  overallScore: number;
  breakdown: {
    readability: ReadabilityResult;
    seo: SEOValidationResult;
    brandCompliance: BrandComplianceResult;
    grammar: GrammarValidationResult;
    structure: StructureValidationResult;
  };
  recommendations: QualityRecommendation[];
  aiCosts?: {
    totalCost: number;
    costByValidator: Record<string, number>;
  };
  validatedAt: string;
}

export interface ReadabilityResult {
  score: number;
  gradeLevel: number;
  fleschKincaidScore: number;
  averageSentenceLength: number;
  averageWordsPerSentence: number;
  complexWords: number;
  recommendations: string[];
}

export interface SEOValidationResult {
  score: number;
  keywordDensity: Record<string, number>;
  metaDescription: {
    present: boolean;
    length: number;
    optimal: boolean;
  };
  headingStructure: {
    h1Count: number;
    h2Count: number;
    h3Count: number;
    properHierarchy: boolean;
  };
  wordCount: number;
  recommendations: string[];
}

export interface BrandComplianceResult {
  score: number;
  toneMatch: number;
  vocabularyCompliance: number;
  prohibitedWordsFound: string[];
  recommendations: string[];
}

export interface GrammarValidationResult {
  score: number;
  errors: Array<{
    type: 'spelling' | 'grammar' | 'punctuation' | 'style';
    message: string;
    position: { start: number; end: number };
    suggestions: string[];
  }>;
  recommendations: string[];
}

export interface StructureValidationResult {
  score: number;
  hasIntroduction: boolean;
  hasConclusion: boolean;
  paragraphCount: number;
  averageParagraphLength: number;
  listUsage: number;
  recommendations: string[];
}

export interface QualityRecommendation {
  type: 'critical' | 'important' | 'suggestion';
  category: 'readability' | 'seo' | 'brand' | 'grammar' | 'structure';
  message: string;
  impact: string;
  action: string;
  priority: number;
}

export class QualityValidator {
  private seoAnalyzer: SEOAnalyzer;
  private costTracker: AIAgentCostTracker;

  constructor() {
    this.seoAnalyzer = new SEOAnalyzer();
    this.costTracker = new AIAgentCostTracker();
  }

  /**
   * Validate content quality across all dimensions
   */
  async validateContent(content: string, config: QualityConfig = {}): Promise<QualityValidationResult> {
    console.log('🔍 Starting comprehensive quality validation...');

    const startTime = Date.now();

    try {
      // Run all validations in parallel
      const [
        readabilityResult,
        seoResult,
        brandResult,
        grammarResult,
        structureResult
      ] = await Promise.all([
        this.validateReadability(content, config),
        this.validateSEO(content, config),
        this.validateBrandCompliance(content, config),
        this.validateGrammar(content, config),
        this.validateStructure(content, config)
      ]);

      // Calculate overall score
      const overallScore = this.calculateOverallScore({
        readability: readabilityResult,
        seo: seoResult,
        brandCompliance: brandResult,
        grammar: grammarResult,
        structure: structureResult
      });

      // Generate recommendations
      const recommendations = this.generateRecommendations({
        readability: readabilityResult,
        seo: seoResult,
        brandCompliance: brandResult,
        grammar: grammarResult,
        structure: structureResult
      });

      const result: QualityValidationResult = {
        overallScore,
        breakdown: {
          readability: readabilityResult,
          seo: seoResult,
          brandCompliance: brandResult,
          grammar: grammarResult,
          structure: structureResult
        },
        recommendations,
        aiCosts: {
          totalCost: this.costTracker.getSessionCosts().reduce((sum, cost) => sum + cost.cost, 0),
          costByValidator: this.groupCostsByValidator()
        },
        validatedAt: new Date().toISOString()
      };

      console.log(`✅ Quality validation completed in ${Date.now() - startTime}ms`);
      console.log(`📊 Overall quality score: ${overallScore}/100`);

      return result;

    } catch (error) {
      console.error('❌ Quality validation failed:', error);
      throw error;
    }
  }

  /**
   * Validate readability metrics
   */
  private async validateReadability(content: string, config: QualityConfig): Promise<ReadabilityResult> {
    const words = content.split(/\s+/).filter(word => word.length > 0);
    const sentences = content.split(/[.!?]+/).filter(sentence => sentence.trim().length > 0);
    const syllables = this.countSyllables(content);
    
    const averageWordsPerSentence = words.length / sentences.length;
    const averageSyllablesPerWord = syllables / words.length;
    
    // Flesch-Kincaid Grade Level
    const gradeLevel = 0.39 * averageWordsPerSentence + 11.8 * averageSyllablesPerWord - 15.59;
    
    // Flesch Reading Ease Score
    const fleschScore = 206.835 - 1.015 * averageWordsPerSentence - 84.6 * averageSyllablesPerWord;
    
    // Complex words (3+ syllables)
    const complexWords = words.filter(word => this.countWordSyllables(word) >= 3).length;
    
    const recommendations: string[] = [];
    
    if (gradeLevel > 12) {
      recommendations.push('Consider simplifying sentence structure to improve readability');
    }
    
    if (fleschScore < 60) {
      recommendations.push('Content may be difficult to read. Consider shorter sentences and simpler words');
    }
    
    if (averageWordsPerSentence > 20) {
      recommendations.push('Average sentence length is high. Break down complex sentences');
    }

    // Calculate readability score (0-100)
    let score = 100;
    if (gradeLevel > 12) score -= 20;
    if (fleschScore < 60) score -= 15;
    if (averageWordsPerSentence > 20) score -= 10;
    if (complexWords / words.length > 0.15) score -= 10;

    return {
      score: Math.max(score, 0),
      gradeLevel: Math.max(gradeLevel, 0),
      fleschKincaidScore: fleschScore,
      averageSentenceLength: averageWordsPerSentence,
      averageWordsPerSentence,
      complexWords,
      recommendations
    };
  }

  /**
   * Validate SEO quality
   */
  private async validateSEO(content: string, config: QualityConfig): Promise<SEOValidationResult> {
    // Use existing SEO analyzer
    const seoAnalysis = await this.seoAnalyzer.analyzeContent(content, {
      targetKeywords: config.seoConfig?.targetKeywords || [],
      includeReadability: false // We handle readability separately
    });

    const wordCount = content.split(/\s+/).filter(word => word.length > 0).length;
    const headings = this.extractHeadings(content);
    
    const recommendations: string[] = [];
    
    if (wordCount < 300) {
      recommendations.push('Content is too short for optimal SEO. Aim for at least 300 words');
    }
    
    if (headings.h1 === 0) {
      recommendations.push('Add an H1 heading for better SEO structure');
    }
    
    if (headings.h1 > 1) {
      recommendations.push('Use only one H1 heading per page');
    }

    return {
      score: seoAnalysis.overallSEOScore,
      keywordDensity: seoAnalysis.keywordAnalysis?.density || {},
      metaDescription: {
        present: false, // TODO: Extract from meta tags
        length: 0,
        optimal: false
      },
      headingStructure: {
        h1Count: headings.h1,
        h2Count: headings.h2,
        h3Count: headings.h3,
        properHierarchy: this.validateHeadingHierarchy(headings)
      },
      wordCount,
      recommendations
    };
  }

  /**
   * Validate brand compliance
   */
  private async validateBrandCompliance(content: string, config: QualityConfig): Promise<BrandComplianceResult> {
    const brandGuidelines = config.brandGuidelines || {};
    const words = content.toLowerCase().split(/\s+/);
    
    let score = 100;
    const recommendations: string[] = [];
    const prohibitedWordsFound: string[] = [];
    
    // Check for prohibited words
    if (brandGuidelines.prohibitedWords) {
      for (const prohibitedWord of brandGuidelines.prohibitedWords) {
        if (words.includes(prohibitedWord.toLowerCase())) {
          prohibitedWordsFound.push(prohibitedWord);
          score -= 10;
        }
      }
    }
    
    // Basic tone analysis (simplified)
    const toneMatch = this.analyzeTone(content, brandGuidelines.tone);
    const vocabularyCompliance = this.analyzeVocabulary(content, brandGuidelines.vocabulary);
    
    if (prohibitedWordsFound.length > 0) {
      recommendations.push(`Remove prohibited words: ${prohibitedWordsFound.join(', ')}`);
    }
    
    if (toneMatch < 70) {
      recommendations.push(`Adjust tone to match brand guidelines (${brandGuidelines.tone})`);
    }

    return {
      score: Math.max(score, 0),
      toneMatch,
      vocabularyCompliance,
      prohibitedWordsFound,
      recommendations
    };
  }

  /**
   * Validate grammar and spelling
   */
  private async validateGrammar(content: string, config: QualityConfig): Promise<GrammarValidationResult> {
    // Track AI cost for grammar checking
    return await this.costTracker.trackAICall(async () => {
      // Simplified grammar validation (in production, integrate with Grammarly API or similar)
      const errors = this.detectBasicGrammarErrors(content);
      
      let score = 100 - (errors.length * 5);
      score = Math.max(score, 0);
      
      const recommendations: string[] = [];
      
      if (errors.length > 0) {
        recommendations.push(`Fix ${errors.length} grammar/spelling errors`);
      }
      
      if (errors.length > 10) {
        recommendations.push('Consider using a grammar checking tool for thorough review');
      }

      return {
        score,
        errors,
        recommendations
      };
    }, 'grammar-validator');
  }

  /**
   * Validate content structure
   */
  private async validateStructure(content: string, config: QualityConfig): Promise<StructureValidationResult> {
    const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim().length > 0);
    const hasIntroduction = this.detectIntroduction(content);
    const hasConclusion = this.detectConclusion(content);
    const listUsage = (content.match(/^\s*[-*•]\s/gm) || []).length;
    
    let score = 100;
    const recommendations: string[] = [];
    
    if (!hasIntroduction) {
      score -= 15;
      recommendations.push('Add a clear introduction to engage readers');
    }
    
    if (!hasConclusion) {
      score -= 15;
      recommendations.push('Add a conclusion to summarize key points');
    }
    
    if (paragraphs.length < 3) {
      score -= 10;
      recommendations.push('Break content into more paragraphs for better readability');
    }
    
    const averageParagraphLength = paragraphs.reduce((sum, p) => sum + p.length, 0) / paragraphs.length;
    
    if (averageParagraphLength > 500) {
      score -= 10;
      recommendations.push('Consider shorter paragraphs for better readability');
    }

    return {
      score: Math.max(score, 0),
      hasIntroduction,
      hasConclusion,
      paragraphCount: paragraphs.length,
      averageParagraphLength,
      listUsage,
      recommendations
    };
  }

  // Helper methods
  private calculateOverallScore(breakdown: any): number {
    const weights = {
      readability: 0.25,
      seo: 0.25,
      brandCompliance: 0.15,
      grammar: 0.20,
      structure: 0.15
    };

    return Math.round(
      breakdown.readability.score * weights.readability +
      breakdown.seo.score * weights.seo +
      breakdown.brandCompliance.score * weights.brandCompliance +
      breakdown.grammar.score * weights.grammar +
      breakdown.structure.score * weights.structure
    );
  }

  private generateRecommendations(breakdown: any): QualityRecommendation[] {
    const recommendations: QualityRecommendation[] = [];
    
    // Add recommendations from each validator
    Object.entries(breakdown).forEach(([category, result]: [string, any]) => {
      if (result.recommendations) {
        result.recommendations.forEach((rec: string, index: number) => {
          recommendations.push({
            type: result.score < 60 ? 'critical' : result.score < 80 ? 'important' : 'suggestion',
            category: category as any,
            message: rec,
            impact: this.getImpactDescription(result.score),
            action: rec,
            priority: result.score < 60 ? 1 : result.score < 80 ? 2 : 3
          });
        });
      }
    });

    return recommendations.sort((a, b) => a.priority - b.priority);
  }

  private getImpactDescription(score: number): string {
    if (score < 60) return 'High impact on content quality';
    if (score < 80) return 'Medium impact on content quality';
    return 'Low impact on content quality';
  }

  private groupCostsByValidator(): Record<string, number> {
    const costs = this.costTracker.getSessionCosts();
    return costs.reduce((acc, cost) => {
      acc[cost.agentType] = (acc[cost.agentType] || 0) + cost.cost;
      return acc;
    }, {} as Record<string, number>);
  }

  // Utility methods for text analysis
  private countSyllables(text: string): number {
    const words = text.toLowerCase().split(/\s+/);
    return words.reduce((total, word) => total + this.countWordSyllables(word), 0);
  }

  private countWordSyllables(word: string): number {
    word = word.toLowerCase();
    if (word.length <= 3) return 1;
    
    word = word.replace(/(?:[^laeiouy]es|ed|[^laeiouy]e)$/, '');
    word = word.replace(/^y/, '');
    
    const matches = word.match(/[aeiouy]{1,2}/g);
    return matches ? matches.length : 1;
  }

  private extractHeadings(content: string): { h1: number; h2: number; h3: number } {
    const h1Count = (content.match(/^#\s/gm) || []).length;
    const h2Count = (content.match(/^##\s/gm) || []).length;
    const h3Count = (content.match(/^###\s/gm) || []).length;
    
    return { h1: h1Count, h2: h2Count, h3: h3Count };
  }

  private validateHeadingHierarchy(headings: { h1: number; h2: number; h3: number }): boolean {
    return headings.h1 === 1 && headings.h2 >= 0 && headings.h3 >= 0;
  }

  private analyzeTone(content: string, targetTone?: string): number {
    // Simplified tone analysis - in production, use AI/ML models
    if (!targetTone) return 85;
    
    const words = content.toLowerCase().split(/\s+/);
    const toneWords = {
      professional: ['expertise', 'solution', 'strategy', 'analysis', 'implementation'],
      casual: ['easy', 'simple', 'fun', 'cool', 'awesome'],
      friendly: ['welcome', 'help', 'support', 'together', 'community'],
      authoritative: ['proven', 'established', 'leading', 'expert', 'authority']
    };
    
    const targetWords = toneWords[targetTone as keyof typeof toneWords] || [];
    const matches = words.filter(word => targetWords.includes(word)).length;
    
    return Math.min(85 + (matches * 5), 100);
  }

  private analyzeVocabulary(content: string, vocabulary?: string[]): number {
    if (!vocabulary || vocabulary.length === 0) return 85;
    
    const words = content.toLowerCase().split(/\s+/);
    const matches = words.filter(word => vocabulary.includes(word)).length;
    
    return Math.min(70 + (matches * 2), 100);
  }

  private detectBasicGrammarErrors(content: string): Array<{
    type: 'spelling' | 'grammar' | 'punctuation' | 'style';
    message: string;
    position: { start: number; end: number };
    suggestions: string[];
  }> {
    const errors = [];
    
    // Basic punctuation checks
    if (!content.trim().endsWith('.') && !content.trim().endsWith('!') && !content.trim().endsWith('?')) {
      errors.push({
        type: 'punctuation' as const,
        message: 'Content should end with proper punctuation',
        position: { start: content.length - 1, end: content.length },
        suggestions: ['Add a period, exclamation mark, or question mark']
      });
    }
    
    // Check for double spaces
    const doubleSpaces = content.match(/  +/g);
    if (doubleSpaces) {
      errors.push({
        type: 'style' as const,
        message: 'Multiple consecutive spaces found',
        position: { start: 0, end: 0 },
        suggestions: ['Replace multiple spaces with single spaces']
      });
    }
    
    return errors;
  }

  private detectIntroduction(content: string): boolean {
    const firstParagraph = content.split(/\n\s*\n/)[0];
    const introWords = ['introduction', 'overview', 'welcome', 'begin', 'start', 'first'];
    return introWords.some(word => firstParagraph.toLowerCase().includes(word)) || firstParagraph.length > 100;
  }

  private detectConclusion(content: string): boolean {
    const paragraphs = content.split(/\n\s*\n/);
    const lastParagraph = paragraphs[paragraphs.length - 1];
    const conclusionWords = ['conclusion', 'summary', 'finally', 'in summary', 'to conclude', 'overall'];
    return conclusionWords.some(word => lastParagraph.toLowerCase().includes(word));
  }
}
