/**
 * Quality Scorer
 * 
 * Calculates overall quality scores and provides detailed scoring breakdown
 */

import { ReadabilityAnalysis } from './readability-analyzer';
import { SEOValidationResult } from './quality-validator';
import { BrandComplianceResult } from './brand-compliance-checker';

export interface QualityScoreWeights {
  readability: number;
  seo: number;
  brandCompliance: number;
  grammar: number;
  structure: number;
  engagement: number;
  originality: number;
}

export interface QualityScoreResult {
  overallScore: number;
  categoryScores: {
    readability: number;
    seo: number;
    brandCompliance: number;
    grammar: number;
    structure: number;
    engagement: number;
    originality: number;
  };
  weightedScores: {
    readability: number;
    seo: number;
    brandCompliance: number;
    grammar: number;
    structure: number;
    engagement: number;
    originality: number;
  };
  qualityGrade: 'Excellent' | 'Good' | 'Fair' | 'Poor' | 'Very Poor';
  strengths: string[];
  weaknesses: string[];
  improvementPotential: number;
  benchmarkComparison: {
    industry: string;
    percentile: number;
    comparison: 'Above Average' | 'Average' | 'Below Average';
  };
}

export interface EngagementMetrics {
  score: number;
  factors: {
    hookStrength: number;
    emotionalAppeal: number;
    callToActionPresence: number;
    storytellingElements: number;
    personalConnection: number;
  };
  recommendations: string[];
}

export interface OriginalityMetrics {
  score: number;
  factors: {
    uniqueInsights: number;
    freshPerspective: number;
    creativeElements: number;
    valueAddition: number;
  };
  recommendations: string[];
}

export class QualityScorer {
  private defaultWeights: QualityScoreWeights = {
    readability: 0.20,
    seo: 0.20,
    brandCompliance: 0.15,
    grammar: 0.15,
    structure: 0.10,
    engagement: 0.10,
    originality: 0.10
  };

  /**
   * Calculate overall quality score from individual assessments
   */
  calculateOverallScore(
    assessments: {
      readability?: ReadabilityAnalysis;
      seo?: SEOValidationResult;
      brandCompliance?: BrandComplianceResult;
      grammar?: { score: number };
      structure?: { score: number };
    },
    content: string,
    weights: Partial<QualityScoreWeights> = {}
  ): QualityScoreResult {
    const finalWeights = { ...this.defaultWeights, ...weights };
    
    // Extract individual scores
    const categoryScores = {
      readability: assessments.readability?.score || 0,
      seo: assessments.seo?.score || 0,
      brandCompliance: assessments.brandCompliance?.overallScore || 0,
      grammar: assessments.grammar?.score || 0,
      structure: assessments.structure?.score || 0,
      engagement: this.calculateEngagementScore(content).score,
      originality: this.calculateOriginalityScore(content).score
    };

    // Calculate weighted scores
    const weightedScores = {
      readability: categoryScores.readability * finalWeights.readability,
      seo: categoryScores.seo * finalWeights.seo,
      brandCompliance: categoryScores.brandCompliance * finalWeights.brandCompliance,
      grammar: categoryScores.grammar * finalWeights.grammar,
      structure: categoryScores.structure * finalWeights.structure,
      engagement: categoryScores.engagement * finalWeights.engagement,
      originality: categoryScores.originality * finalWeights.originality
    };

    // Calculate overall score
    const overallScore = Math.round(
      Object.values(weightedScores).reduce((sum, score) => sum + score, 0)
    );

    // Determine quality grade
    const qualityGrade = this.getQualityGrade(overallScore);

    // Identify strengths and weaknesses
    const { strengths, weaknesses } = this.identifyStrengthsAndWeaknesses(categoryScores);

    // Calculate improvement potential
    const improvementPotential = this.calculateImprovementPotential(categoryScores, finalWeights);

    // Benchmark comparison
    const benchmarkComparison = this.getBenchmarkComparison(overallScore, 'general');

    return {
      overallScore,
      categoryScores,
      weightedScores,
      qualityGrade,
      strengths,
      weaknesses,
      improvementPotential,
      benchmarkComparison
    };
  }

  /**
   * Calculate engagement score
   */
  private calculateEngagementScore(content: string): EngagementMetrics {
    const factors = {
      hookStrength: this.analyzeHookStrength(content),
      emotionalAppeal: this.analyzeEmotionalAppeal(content),
      callToActionPresence: this.analyzeCallToAction(content),
      storytellingElements: this.analyzeStorytelling(content),
      personalConnection: this.analyzePersonalConnection(content)
    };

    const score = Math.round(
      Object.values(factors).reduce((sum, factor) => sum + factor, 0) / Object.keys(factors).length
    );

    const recommendations = this.generateEngagementRecommendations(factors);

    return { score, factors, recommendations };
  }

  /**
   * Calculate originality score
   */
  private calculateOriginalityScore(content: string): OriginalityMetrics {
    const factors = {
      uniqueInsights: this.analyzeUniqueInsights(content),
      freshPerspective: this.analyzeFreshPerspective(content),
      creativeElements: this.analyzeCreativeElements(content),
      valueAddition: this.analyzeValueAddition(content)
    };

    const score = Math.round(
      Object.values(factors).reduce((sum, factor) => sum + factor, 0) / Object.keys(factors).length
    );

    const recommendations = this.generateOriginalityRecommendations(factors);

    return { score, factors, recommendations };
  }

  /**
   * Analyze hook strength (opening engagement)
   */
  private analyzeHookStrength(content: string): number {
    const firstSentence = content.split(/[.!?]/)[0];
    let score = 50; // Base score

    // Check for question hooks
    if (firstSentence.includes('?')) score += 20;
    
    // Check for surprising statements
    if (firstSentence.match(/\b(surprising|shocking|amazing|incredible)\b/i)) score += 15;
    
    // Check for statistics
    if (firstSentence.match(/\d+%|\d+\s*(percent|million|billion)/i)) score += 15;
    
    // Check for direct address
    if (firstSentence.match(/\b(you|your)\b/i)) score += 10;
    
    // Penalize weak openings
    if (firstSentence.match(/^(this|that|it|there)\s/i)) score -= 15;

    return Math.max(Math.min(score, 100), 0);
  }

  /**
   * Analyze emotional appeal
   */
  private analyzeEmotionalAppeal(content: string): number {
    let score = 50;
    
    // Positive emotion words
    const positiveWords = ['amazing', 'incredible', 'fantastic', 'excellent', 'outstanding', 'remarkable'];
    const positiveMatches = positiveWords.filter(word => 
      content.toLowerCase().includes(word)
    ).length;
    score += positiveMatches * 5;

    // Power words
    const powerWords = ['transform', 'revolutionize', 'breakthrough', 'ultimate', 'proven', 'guaranteed'];
    const powerMatches = powerWords.filter(word => 
      content.toLowerCase().includes(word)
    ).length;
    score += powerMatches * 3;

    // Sensory words
    const sensoryWords = ['see', 'hear', 'feel', 'taste', 'touch', 'imagine', 'picture'];
    const sensoryMatches = sensoryWords.filter(word => 
      content.toLowerCase().includes(word)
    ).length;
    score += sensoryMatches * 2;

    return Math.max(Math.min(score, 100), 0);
  }

  /**
   * Analyze call-to-action presence
   */
  private analyzeCallToAction(content: string): number {
    let score = 0;
    
    const ctaWords = ['click', 'download', 'subscribe', 'sign up', 'get started', 'learn more', 'contact', 'buy now'];
    const ctaMatches = ctaWords.filter(word => 
      content.toLowerCase().includes(word)
    ).length;
    
    if (ctaMatches > 0) score = 70 + (ctaMatches * 5);
    
    // Check for imperative verbs
    const imperativePattern = /\b(start|begin|discover|explore|find|get|try|join)\b/gi;
    const imperativeMatches = content.match(imperativePattern);
    if (imperativeMatches) score += imperativeMatches.length * 2;

    return Math.max(Math.min(score, 100), 0);
  }

  /**
   * Analyze storytelling elements
   */
  private analyzeStorytelling(content: string): number {
    let score = 50;
    
    // Check for narrative elements
    const narrativeWords = ['story', 'journey', 'experience', 'adventure', 'challenge', 'solution'];
    const narrativeMatches = narrativeWords.filter(word => 
      content.toLowerCase().includes(word)
    ).length;
    score += narrativeMatches * 8;

    // Check for temporal markers
    const temporalWords = ['first', 'then', 'next', 'finally', 'meanwhile', 'suddenly'];
    const temporalMatches = temporalWords.filter(word => 
      content.toLowerCase().includes(word)
    ).length;
    score += temporalMatches * 5;

    // Check for character elements
    if (content.match(/\b(I|we|our team|customer|client)\b/gi)) score += 10;

    return Math.max(Math.min(score, 100), 0);
  }

  /**
   * Analyze personal connection
   */
  private analyzePersonalConnection(content: string): number {
    let score = 50;
    
    // Direct address
    const youMatches = content.match(/\byou\b/gi);
    if (youMatches) score += Math.min(youMatches.length * 2, 20);

    // Personal pronouns
    const personalPronouns = content.match(/\b(I|we|us|our|my)\b/gi);
    if (personalPronouns) score += Math.min(personalPronouns.length, 15);

    // Questions to reader
    const questions = content.match(/\?/g);
    if (questions) score += Math.min(questions.length * 5, 15);

    return Math.max(Math.min(score, 100), 0);
  }

  /**
   * Analyze unique insights
   */
  private analyzeUniqueInsights(content: string): number {
    let score = 50;
    
    // Look for insight indicators
    const insightWords = ['insight', 'discovery', 'revelation', 'finding', 'research shows', 'study reveals'];
    const insightMatches = insightWords.filter(word => 
      content.toLowerCase().includes(word)
    ).length;
    score += insightMatches * 10;

    // Look for data and statistics
    const dataPattern = /\d+%|\d+\s*(percent|million|billion|study|research)/gi;
    const dataMatches = content.match(dataPattern);
    if (dataMatches) score += Math.min(dataMatches.length * 5, 25);

    return Math.max(Math.min(score, 100), 0);
  }

  /**
   * Analyze fresh perspective
   */
  private analyzeFreshPerspective(content: string): number {
    let score = 50;
    
    // Look for contrarian indicators
    const contrarianWords = ['however', 'contrary', 'unlike', 'different', 'alternative', 'new approach'];
    const contrarianMatches = contrarianWords.filter(word => 
      content.toLowerCase().includes(word)
    ).length;
    score += contrarianMatches * 8;

    // Look for innovation language
    const innovationWords = ['innovative', 'revolutionary', 'breakthrough', 'cutting-edge', 'novel'];
    const innovationMatches = innovationWords.filter(word => 
      content.toLowerCase().includes(word)
    ).length;
    score += innovationMatches * 6;

    return Math.max(Math.min(score, 100), 0);
  }

  /**
   * Analyze creative elements
   */
  private analyzeCreativeElements(content: string): number {
    let score = 50;
    
    // Look for metaphors and analogies
    const metaphorWords = ['like', 'as if', 'imagine', 'picture', 'think of'];
    const metaphorMatches = metaphorWords.filter(word => 
      content.toLowerCase().includes(word)
    ).length;
    score += metaphorMatches * 5;

    // Look for vivid descriptions
    const descriptiveWords = ['vibrant', 'crystal clear', 'razor-sharp', 'lightning-fast'];
    const descriptiveMatches = descriptiveWords.filter(word => 
      content.toLowerCase().includes(word)
    ).length;
    score += descriptiveMatches * 7;

    return Math.max(Math.min(score, 100), 0);
  }

  /**
   * Analyze value addition
   */
  private analyzeValueAddition(content: string): number {
    let score = 50;
    
    // Look for value indicators
    const valueWords = ['benefit', 'advantage', 'value', 'save', 'improve', 'increase', 'reduce'];
    const valueMatches = valueWords.filter(word => 
      content.toLowerCase().includes(word)
    ).length;
    score += valueMatches * 4;

    // Look for actionable advice
    const actionWords = ['how to', 'step by step', 'guide', 'tutorial', 'tips', 'strategies'];
    const actionMatches = actionWords.filter(word => 
      content.toLowerCase().includes(word)
    ).length;
    score += actionMatches * 6;

    return Math.max(Math.min(score, 100), 0);
  }

  /**
   * Generate engagement recommendations
   */
  private generateEngagementRecommendations(factors: any): string[] {
    const recommendations: string[] = [];
    
    if (factors.hookStrength < 70) {
      recommendations.push('Strengthen opening with a compelling hook (question, statistic, or surprising statement)');
    }
    
    if (factors.emotionalAppeal < 60) {
      recommendations.push('Add more emotional language and power words to increase appeal');
    }
    
    if (factors.callToActionPresence < 50) {
      recommendations.push('Include clear call-to-action statements to guide reader behavior');
    }
    
    if (factors.storytellingElements < 60) {
      recommendations.push('Incorporate storytelling elements to make content more engaging');
    }
    
    if (factors.personalConnection < 60) {
      recommendations.push('Use more direct address and personal pronouns to connect with readers');
    }

    return recommendations;
  }

  /**
   * Generate originality recommendations
   */
  private generateOriginalityRecommendations(factors: any): string[] {
    const recommendations: string[] = [];
    
    if (factors.uniqueInsights < 70) {
      recommendations.push('Include more unique insights, data, or research findings');
    }
    
    if (factors.freshPerspective < 60) {
      recommendations.push('Offer a fresh perspective or contrarian viewpoint');
    }
    
    if (factors.creativeElements < 60) {
      recommendations.push('Add creative elements like metaphors, analogies, or vivid descriptions');
    }
    
    if (factors.valueAddition < 70) {
      recommendations.push('Enhance value proposition with more actionable advice and benefits');
    }

    return recommendations;
  }

  /**
   * Get quality grade based on overall score
   */
  private getQualityGrade(score: number): QualityScoreResult['qualityGrade'] {
    if (score >= 90) return 'Excellent';
    if (score >= 80) return 'Good';
    if (score >= 70) return 'Fair';
    if (score >= 60) return 'Poor';
    return 'Very Poor';
  }

  /**
   * Identify strengths and weaknesses
   */
  private identifyStrengthsAndWeaknesses(scores: any): { strengths: string[]; weaknesses: string[] } {
    const strengths: string[] = [];
    const weaknesses: string[] = [];
    
    Object.entries(scores).forEach(([category, score]: [string, any]) => {
      if (score >= 80) {
        strengths.push(category.charAt(0).toUpperCase() + category.slice(1));
      } else if (score < 60) {
        weaknesses.push(category.charAt(0).toUpperCase() + category.slice(1));
      }
    });

    return { strengths, weaknesses };
  }

  /**
   * Calculate improvement potential
   */
  private calculateImprovementPotential(scores: any, weights: QualityScoreWeights): number {
    let maxPossibleImprovement = 0;
    
    Object.entries(scores).forEach(([category, score]: [string, any]) => {
      const weight = weights[category as keyof QualityScoreWeights];
      const improvement = (100 - score) * weight;
      maxPossibleImprovement += improvement;
    });

    return Math.round(maxPossibleImprovement);
  }

  /**
   * Get benchmark comparison
   */
  private getBenchmarkComparison(score: number, industry: string): QualityScoreResult['benchmarkComparison'] {
    // Simplified benchmark - in production, use real industry data
    const industryAverage = 75;
    const percentile = score > industryAverage ? 75 : score === industryAverage ? 50 : 25;
    
    let comparison: 'Above Average' | 'Average' | 'Below Average';
    if (score > industryAverage + 5) comparison = 'Above Average';
    else if (score < industryAverage - 5) comparison = 'Below Average';
    else comparison = 'Average';

    return {
      industry,
      percentile,
      comparison
    };
  }
}
