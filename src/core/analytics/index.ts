/**
 * Analytics Module Exports
 *
 * Centralized exports for all analytics components
 */

// Core Analytics Engine
export { AnalyticsEngine } from './AnalyticsEngine';

// Workflow Analytics Engine
export {
  WorkflowAnalyticsEngine,
  AIAgentCostTracker,
  type AIAgentCostMetrics,
  type CostTrendData,
  type WorkflowCostBreakdown,
  type CostReport,
  type CostOptimizationRecommendation,
  type WorkflowAnalyticsMetrics,
  type StepPerformanceMetrics,
  type WorkflowTrendData,
  type WorkflowQualityMetrics,
  type AnalyticsFilters
} from './workflow-analytics-engine';

// Data Collection
export { 
  DataCollector,
  type CollaborationData,
  type TimeRange,
  type PerformanceData,
  type QualityMetrics
} from './DataCollector';

// Metrics Calculation
export {
  MetricsCalculator,
  type PerformanceMetrics,
  type ThroughputMetric,
  type LatencyMetric,
  type ReliabilityMetric,
  type EfficiencyMetric,
  type AnalyticsSummary,
  type DataPoint
} from './MetricsCalculator';

// Trend Analysis
export {
  TrendAnalyzer,
  type TrendAnalysis,
  type SeasonalPattern,
  type AnomalyDetection,
  type ForecastResult
} from './TrendAnalyzer';

// Report Generation
export {
  ReportGenerator,
  type AnalyticsReport,
  type QualityTrends,
  type AgentAnalytics,
  type AnalyticsRecommendation,
  type ExecutiveSummary
} from './ReportGenerator';

// Additional Types
export type {
  PerformanceIssue,
  Optimization
} from './AnalyticsEngine';
