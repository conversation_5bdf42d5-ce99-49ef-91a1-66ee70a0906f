/**
 * Analytics Hook
 * 
 * React hook for managing analytics data and operations
 */

'use client';

import { useState, useEffect, useCallback } from 'react';

export interface AnalyticsMetrics {
  totalExecutions: number;
  successRate: number;
  averageExecutionTime: number;
  aiCostMetrics: {
    totalCost: number;
    averageCostPerWorkflow: number;
    totalTokensUsed: number;
    tokenEfficiency: number;
    costByAgent: Record<string, number>;
    costByStep: Record<string, number>;
    costTrends: Array<{
      timestamp: string;
      cost: number;
      tokens: number;
      executionId: string;
      workflowType: string;
    }>;
  };
  qualityMetrics: {
    averageQualityScore: number;
    improvementRate: number;
    qualityTrends: Array<{
      timestamp: string;
      score: number;
      executionId: string;
    }>;
    qualityByWorkflowType: Record<string, number>;
  };
  stepPerformance: Array<{
    stepId: string;
    stepName: string;
    averageExecutionTime: number;
    successRate: number;
    errorRate: number;
    averageCost: number;
    bottleneckScore: number;
  }>;
  trends: Array<{
    timestamp: string;
    executionCount: number;
    successRate: number;
    averageTime: number;
    totalCost: number;
  }>;
}

export interface RealTimeMetrics {
  activeExecutions: number;
  currentThroughput: number;
  systemHealth: number;
  budgetUtilization: number;
}

export interface CostReport {
  totalCost: number;
  totalExecutions: number;
  averageCostPerExecution: number;
  costByWorkflowType: Record<string, number>;
  costByAgent: Record<string, number>;
  trends: Array<{
    timestamp: string;
    cost: number;
    tokens: number;
    executionId: string;
    workflowType: string;
  }>;
  budgetUtilization: number;
  recommendations: Array<{
    type: string;
    description: string;
    potentialSavings: number;
    implementation: string;
    priority: string;
  }>;
}

export interface AnalyticsFilters {
  workflowType?: string;
  status?: string;
  dateRange?: {
    start: string;
    end: string;
  };
  minCost?: number;
  maxCost?: number;
}

export interface AnalyticsData {
  metrics: AnalyticsMetrics;
  realTimeMetrics: RealTimeMetrics;
  costReport: CostReport;
  timeRange: string;
  filters?: AnalyticsFilters;
  generatedAt: string;
}

export interface UseAnalyticsOptions {
  timeRange?: string;
  filters?: AnalyticsFilters;
  autoRefresh?: boolean;
  refreshInterval?: number; // in milliseconds
}

export interface UseAnalyticsReturn {
  data: AnalyticsData | null;
  loading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
  trackCost: (executionId: string, agentType: string, tokens: number, cost: number) => Promise<void>;
  calculateWorkflowCost: (executionId: string) => Promise<any>;
  exportData: (format: 'json' | 'csv' | 'pdf') => Promise<void>;
  setFilters: (filters: AnalyticsFilters) => void;
  setTimeRange: (timeRange: string) => void;
}

export function useAnalytics(options: UseAnalyticsOptions = {}): UseAnalyticsReturn {
  const {
    timeRange: initialTimeRange = '30d',
    filters: initialFilters,
    autoRefresh = false,
    refreshInterval = 300000 // 5 minutes
  } = options;

  const [data, setData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState(initialTimeRange);
  const [filters, setFilters] = useState<AnalyticsFilters>(initialFilters || {});

  const fetchAnalyticsData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Build query parameters
      const params = new URLSearchParams();
      params.append('timeRange', timeRange);
      
      if (filters.workflowType) params.append('workflowId', filters.workflowType);
      if (filters.status) params.append('status', filters.status);
      if (filters.dateRange?.start) params.append('startDate', filters.dateRange.start);
      if (filters.dateRange?.end) params.append('endDate', filters.dateRange.end);

      const response = await fetch(`/api/analytics/metrics?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch analytics data: ${response.statusText}`);
      }
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch analytics data');
      }
      
      setData(result.data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Analytics fetch error:', err);
    } finally {
      setLoading(false);
    }
  }, [timeRange, filters]);

  const trackCost = useCallback(async (
    executionId: string,
    agentType: string,
    tokens: number,
    cost: number
  ) => {
    try {
      const response = await fetch('/api/analytics/metrics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'track_cost',
          data: { executionId, agentType, tokens, cost }
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to track cost');
      }

      // Optionally refresh data after tracking cost
      if (data) {
        await fetchAnalyticsData();
      }
    } catch (err) {
      console.error('Cost tracking error:', err);
      throw err;
    }
  }, [data, fetchAnalyticsData]);

  const calculateWorkflowCost = useCallback(async (executionId: string) => {
    try {
      const response = await fetch('/api/analytics/metrics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'calculate_workflow_cost',
          data: { executionId }
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to calculate workflow cost');
      }

      const result = await response.json();
      return result.data;
    } catch (err) {
      console.error('Workflow cost calculation error:', err);
      throw err;
    }
  }, []);

  const exportData = useCallback(async (format: 'json' | 'csv' | 'pdf') => {
    try {
      const params = new URLSearchParams();
      params.append('format', format);
      params.append('timeRange', timeRange);
      
      if (filters.workflowType) params.append('workflowId', filters.workflowType);

      const response = await fetch(`/api/analytics/export?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error('Export failed');
      }
      
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `analytics-${timeRange}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      console.error('Export error:', err);
      throw err;
    }
  }, [timeRange, filters]);

  const refresh = useCallback(async () => {
    await fetchAnalyticsData();
  }, [fetchAnalyticsData]);

  // Initial data fetch
  useEffect(() => {
    fetchAnalyticsData();
  }, [fetchAnalyticsData]);

  // Auto-refresh setup
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      fetchAnalyticsData();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, fetchAnalyticsData]);

  return {
    data,
    loading,
    error,
    refresh,
    trackCost,
    calculateWorkflowCost,
    exportData,
    setFilters,
    setTimeRange
  };
}

// Utility functions for analytics calculations
export const analyticsUtils = {
  /**
   * Calculate percentage change between two values
   */
  calculatePercentageChange: (current: number, previous: number): number => {
    if (previous === 0) return current > 0 ? 100 : 0;
    return ((current - previous) / previous) * 100;
  },

  /**
   * Format currency values
   */
  formatCurrency: (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  },

  /**
   * Format duration in milliseconds to human readable format
   */
  formatDuration: (milliseconds: number): string => {
    if (milliseconds < 1000) return `${Math.round(milliseconds)}ms`;
    if (milliseconds < 60000) return `${(milliseconds / 1000).toFixed(1)}s`;
    if (milliseconds < 3600000) return `${(milliseconds / 60000).toFixed(1)}m`;
    return `${(milliseconds / 3600000).toFixed(1)}h`;
  },

  /**
   * Calculate trend direction based on data points
   */
  calculateTrend: (dataPoints: number[]): 'improving' | 'declining' | 'stable' => {
    if (dataPoints.length < 2) return 'stable';
    
    const firstHalf = dataPoints.slice(0, Math.floor(dataPoints.length / 2));
    const secondHalf = dataPoints.slice(Math.floor(dataPoints.length / 2));
    
    const firstAvg = firstHalf.reduce((sum, val) => sum + val, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, val) => sum + val, 0) / secondHalf.length;
    
    const change = ((secondAvg - firstAvg) / firstAvg) * 100;
    
    if (change > 5) return 'improving';
    if (change < -5) return 'declining';
    return 'stable';
  },

  /**
   * Generate color based on performance score
   */
  getPerformanceColor: (score: number, threshold: number = 90): string => {
    if (score >= threshold) return 'text-green-600';
    if (score >= threshold * 0.7) return 'text-yellow-600';
    return 'text-red-600';
  }
};
